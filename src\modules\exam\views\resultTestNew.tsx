/* eslint-disable react-native/no-inline-styles */
import {
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import ScreenHeader from '../../../Screen/Layout/header';
import {navigateBack} from '../../../router/router';
import {AppButton, FBottomSheet, Winicon} from 'wini-mobile-components';
import WScreenFooter from '../../../Screen/Layout/footer';
import {TypoSkin} from '../../../assets/skin/typography';
import React, {useEffect, useRef, useState} from 'react';
import {useRoute} from '@react-navigation/native';
import EmptyPage from '../../../Screen/emptyPage';
import {useTranslation} from 'react-i18next';

export default function ResultTestNew() {
  const {t} = useTranslation();
  const bottomSheetRef = useRef<any>(null);
  const route = useRoute<any>();
  const {item} = route.params;
  const [expandedSections, setExpandedSections] = useState<{
    [key: string]: boolean;
  }>({});

  useEffect(() => {
    console.log('📊 Result Data:', item);
    // Auto expand first section
    if (item?.sections?.length > 0) {
      const firstSectionId = item.sections[0].id;
      setExpandedSections({[firstSectionId]: true});
    }
  }, [item]);

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId],
    }));
  };
  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <ScreenHeader
        title={t('exam.resultsTitle')}
        backIcon={<Winicon src="outline/arrows/left-arrow" size={20} />}
        // onBack={() => {
        //   navigateBack();
        // }}
      />
      {/* content */}
      {!item || !item.sections ? (
        <EmptyPage title={t('exam.noResults')} />
      ) : (
        <ScrollView
          style={{
            flex: 1,
            backgroundColor: item?.isPassed
              ? ColorThemes.light.Success_Color_Background
              : ColorThemes.light.Warning_Color_Background,
          }}>
          {/* Header Result */}
          <View
            style={{
              gap: 16,
              alignContent: 'center',
              alignItems: 'center',
              paddingVertical: 40,
              paddingHorizontal: 24,
            }}>
            {/* Score Circle */}
            <View
              style={{
                height: 100,
                width: 100,
                borderRadius: 50,
                backgroundColor: item?.isPassed
                  ? ColorThemes.light.Success_Color_Main
                  : ColorThemes.light.Warning_Color_Main,
                alignItems: 'center',
                justifyContent: 'center',
                shadowColor: item?.isPassed
                  ? ColorThemes.light.Success_Color_Main
                  : ColorThemes.light.Warning_Color_Main,
                shadowOffset: {width: 0, height: 4},
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 8,
              }}>
              <Text
                style={{
                  ...TypoSkin.title1,
                  color: ColorThemes.light.Neutral_Background_Color_Absolute,
                  textAlign: 'center',
                  fontWeight: '700',
                }}>
                {item?.totalScore ?? 0}
              </Text>
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.Neutral_Background_Color_Absolute,
                  textAlign: 'center',
                }}>
                /{item?.maxTotalScore || 0}
              </Text>
            </View>

            {/* Result Status */}
            <Text
              style={{
                textAlign: 'center',
                ...TypoSkin.title2,
                fontWeight: '700',
                color: ColorThemes.light.neutral_text_title_color,
              }}>
              {item?.isPassed
                ? t('exam.congratulations')
                : t('exam.tryAgainWhenReady')}
            </Text>

            {/* Statistics */}
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-around',
                width: '100%',
                marginTop: 16,
              }}>
              <View style={{alignItems: 'center'}}>
                <Text
                  style={{
                    ...TypoSkin.title3,
                    fontWeight: '600',
                    color: ColorThemes.light.Success_Color_Main,
                  }}>
                  {item?.correctAnswers || 0}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.body3,
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                  }}>
                  {'Câu đúng'}
                </Text>
              </View>
              <View style={{alignItems: 'center'}}>
                <Text
                  style={{
                    ...TypoSkin.title3,
                    fontWeight: '600',
                    color: ColorThemes.light.Error_Color_Main,
                  }}>
                  {(item?.answeredQuestions || 0) - (item?.correctAnswers || 0)}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.body3,
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                  }}>
                  {'Câu sai'}
                </Text>
              </View>
              <View style={{alignItems: 'center'}}>
                <Text
                  style={{
                    ...TypoSkin.title3,
                    fontWeight: '600',
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                  }}>
                  {(item?.totalQuestions || 0) - (item?.answeredQuestions || 0)}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.body3,
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                  }}>
                  {'Câu bỏ trống'}
                </Text>
              </View>
            </View>
          </View>

          {/* Sections and Questions */}
          <View
            style={{
              backgroundColor:
                ColorThemes.light.Neutral_Background_Color_Absolute,
              marginHorizontal: 16,
              borderRadius: 12,
              marginBottom: 20,
              overflow: 'hidden',
            }}>
            {/* Flat List Sections */}
            {item?.sections?.map((section: any) => (
              <View key={section.id}>
                {/* Section Header */}
                <TouchableOpacity
                  onPress={() => toggleSection(section.id)}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: 16,
                    backgroundColor:
                      ColorThemes.light.Neutral_Background_Color_Absolute,
                    borderBottomWidth: 0.5,
                    borderBottomColor: '#ccc',
                  }}>
                  <View style={{flex: 1}}>
                    <Text
                      style={{
                        ...TypoSkin.subtitle1,
                        fontWeight: '700',
                        color: section.isPassed
                          ? ColorThemes.light.Success_Color_Main
                          : ColorThemes.light.Error_Color_Main,
                        marginBottom: 4,
                      }}>
                      {section.name}
                    </Text>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <Text
                        style={{
                          ...TypoSkin.body3,
                          color: section.isPassed
                            ? ColorThemes.light.Success_Color_Main
                            : ColorThemes.light.Error_Color_Main,
                          fontWeight: '600',
                          marginRight: 16,
                        }}>
                        {section.isPassed ? 'Đạt' : 'Không đạt'}
                      </Text>
                      <Text
                        style={{
                          ...TypoSkin.body3,
                          color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                        }}>
                        {section.score}/{section.maxScore} điểm{' '}
                        {`(Điểm đạt: ${section.eliminationScore})`}
                      </Text>
                    </View>
                  </View>
                  <Winicon
                    src={
                      expandedSections[section.id]
                        ? 'outline/arrows/arrow-sm-down'
                        : 'outline/arrows/arrow-sm-right'
                    }
                    size={32}
                  />
                </TouchableOpacity>

                {/* Section Content - All Questions Combined */}
                {expandedSections[section.id] && (
                  <View>
                    {/* Questions Grid - All questions from all exams in this section */}
                    <View
                      style={{
                        padding: 16,
                        backgroundColor:
                          ColorThemes.light.neutral_absolute_background_color,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          flexWrap: 'wrap',
                          gap: 8,
                        }}>
                        {(() => {
                          // Combine all questions from all exams in this section
                          const allSectionQuestions: any[] = [];

                          // Calculate starting question number for this section
                          const sectionIndex =
                            item?.sections?.findIndex(
                              (s: any) => s.id === section.id,
                            ) || 0;
                          let startingQuestionNumber = 1;

                          // Add up all questions from previous sections
                          for (let i = 0; i < sectionIndex; i++) {
                            const prevSection = item?.sections?.[i];
                            if (prevSection?.exams) {
                              const prevSectionQuestionCount =
                                prevSection.exams.reduce(
                                  (total: number, exam: any) =>
                                    total + (exam.questions?.length || 0),
                                  0,
                                );
                              startingQuestionNumber +=
                                prevSectionQuestionCount;
                            }
                          }

                          // Sort exams by sort order first
                          const sortedExams =
                            section.exams?.sort((a: any, b: any) => {
                              const sortA = Number(a.sort || a.Sort || 0);
                              const sortB = Number(b.sort || b.Sort || 0);
                              return sortA - sortB;
                            }) || [];

                          // Collect all questions from sorted exams
                          sortedExams.forEach((exam: any) => {
                            exam.questions?.forEach((question: any) => {
                              allSectionQuestions.push({
                                ...question,
                                displayNumber:
                                  startingQuestionNumber +
                                  allSectionQuestions.length,
                                examName: exam.name,
                              });
                            });
                          });

                          return allSectionQuestions.map(
                            (question: any, index: number) => (
                              <TouchableOpacity
                                key={`${question.id}-${index}`}
                                style={{
                                  width: 36,
                                  height: 36,
                                  borderRadius: 18,
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  backgroundColor: question.isCorrect
                                    ? ColorThemes.light.Success_Color_Background
                                    : ColorThemes.light.Error_Color_Background,
                                  borderWidth: 1,
                                  borderColor: question.isCorrect
                                    ? ColorThemes.light.Success_Color_Main
                                    : ColorThemes.light.Error_Color_Main,
                                }}>
                                <Text
                                  style={{
                                    ...TypoSkin.body3,
                                    fontWeight: '600',
                                    fontSize: 12,
                                    color: question.isCorrect
                                      ? ColorThemes.light.Success_Color_Main
                                      : ColorThemes.light.Error_Color_Main,
                                  }}>
                                  {question.displayNumber}
                                </Text>
                              </TouchableOpacity>
                            ),
                          );
                        })()}
                      </View>
                    </View>
                  </View>
                )}
              </View>
            ))}
          </View>
          <View style={{height: 100}} />
        </ScrollView>
      )}
      {/*  */}
      <WScreenFooter
        style={{
          flexDirection: 'row',
          flex: 1,
          gap: 8,
          paddingHorizontal: 16,
        }}>
        <AppButton
          title={t('exam.finish')}
          backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
          textColor={ColorThemes.light.Neutral_Text_Color_Body}
          borderColor="transparent"
          containerStyle={{
            flex: 1,
            borderRadius: 8,
          }}
          onPress={() => {
            // dispatch(ExamActions.resetReducer());
            navigateBack();
            // if (type === ExamType.Try) {
            //   navigateReset(RootScreen.navigateESchoolView);
            // } else {
            //   navigateReset(RootScreen.ProccessCourse, {id: courseId});
            // }
          }}
        />
        <AppButton
          title={t('exam.retake')}
          backgroundColor={ColorThemes.light.Primary_Color_Main}
          textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
          borderColor="transparent"
          containerStyle={{
            flex: 1,
            borderRadius: 8,
          }}
          onPress={() => {
            navigateBack();
            // showBottomSheet({
            //   ref: bottomSheetRef,
            //   enableDismiss: true,
            //   title: t('exam.confirmRetake'),
            //   prefixAction: <View />,
            //   suffixAction: (
            //     <TouchableOpacity
            //       onPress={() => {
            //         hideBottomSheet(bottomSheetRef);
            //       }}
            //       style={{padding: 6, alignItems: 'center'}}>
            //       <Winicon
            //         src="outline/layout/xmark"
            //         size={20}
            //         color={ColorThemes.light.Neutral_Text_Color_Body}
            //       />
            //     </TouchableOpacity>
            //   ),
            //   children: (
            //     <ConFirmReTest ref={bottomSheetRef} id={id} type={type} />
            //   ),
            // });
          }}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
}
