/* eslint-disable react-native/no-inline-styles */
import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
  memo,
  useMemo,
} from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  FlatList,
  Image,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import ScreenHeader from '../../../Screen/Layout/header';
import { navigateBack, RootScreen } from '../../../router/router';
import {
  AppButton,
  Checkbox,
  FBottomSheet,
  FLoading,
  hideBottomSheet,
  showBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import { TypoSkin } from '../../../assets/skin/typography';

import { RadioButton } from 'react-native-paper';
import CountdownTimer from '../components/countDownTimer';
import AnimatedTimerIcon from '../components/AnimatedTimerIcon';
import ClickableImage from '../components/ClickableImage';
import { useDispatch } from 'react-redux';
import store, { AppDispatch } from '../../../redux/store/store';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ExamActions } from '../../../redux/reducers/examReducer';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import RenderHTML from 'react-native-render-html';
import { DataController } from '../../../base/baseController';
import { Sakupi, SakupiType, StatusExam } from '../../../Config/Contanst';
import { randomGID } from '../../../utils/Utils';
import ConfigAPI from '../../../Config/ConfigAPI';
import Sound from 'react-native-sound';
import FastImage from '@d11/react-native-fast-image';
import { BaseDA } from '../../../base/BaseDA';
import { LessonActions } from '../../../redux/reducers/proccessLessonReducer';
import { CustomerActions } from '../../../redux/reducers/CustomerReducer';
import PDFViewerV2 from '../../Course/components/PDFViewerV2';
import PDFViewer from '../../Course/components/PDFViewer';
import { shuffleArray } from '../../../utils/arrayUtils';

// Global helper function to safely render HTML content

// Memoized Answer Component for better performance
const AnswerItem = memo(
  ({
    answer,
    questionId,
    selectionType,
    onSelect,
    index,
  }: {
    answer: any;
    questionId: string;
    selectionType: number;
    onSelect: (questionId: string, answerId: string) => void;
    index: number;
  }) => {
    const handlePress = useCallback(() => {
      console.log(
        `🔘 AnswerItem handlePress: questionId=${questionId}, answerId=${answer.Id}`,
      );
      onSelect(questionId, answer.Id);
    }, [onSelect, questionId, answer.Id]);

    console.log(
      `🎨 AnswerItem render: answerId=${answer.Id}, choose=${answer.choose}`,
    );

    return (
      <TouchableOpacity
        onPress={() => {
          console.log(`👆 TouchableOpacity pressed for answer ${answer.Id}`);
          handlePress();
        }}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: 12,
          paddingVertical: 4,
          borderRadius: 8,
          borderWidth: 1,
          borderColor: answer.choose
            ? ColorThemes.light.Info_Color_Main
            : ColorThemes.light.Neutral_Border_Color_Main,
          backgroundColor: answer.choose
            ? ColorThemes.light.primary_background
            : ColorThemes.light.Neutral_Background_Color_Absoluteary_Color_Main,
        }}>
        {/* Answer Selection Icon */}
        <View style={{ marginRight: 4 }} pointerEvents="none">
          {selectionType === 2 ? (
            <Checkbox
              value={answer.choose ?? false}
              onChange={() => { }} // Disabled - handled by TouchableOpacity
              checkboxStyle={{
                backgroundColor: ColorThemes.light.Info_Color_Main,
              }}
            />
          ) : (
            <RadioButton.Android
              value={answer.Id}
              status={answer.choose ? 'checked' : 'unchecked'}
              color={ColorThemes.light.Info_Color_Main}
              onPress={() => { }} // Disabled - handled by TouchableOpacity
            />
          )}
        </View>

        {/* Answer Content */}
        <View style={{ flex: 1 }}>
          {/* Answer Text */}
          {(answer.Content || answer.Name) && (
            <Text
              style={{
                ...TypoSkin.body2,
                color: answer.choose
                  ? ColorThemes.light.Info_Color_Main
                  : ColorThemes.light.neutral_text_body_color,
                marginBottom: answer.Img ? 8 : 0,
              }}>
              {`${index + 1}. ${answer.Content || answer.Name}`}
            </Text>
          )}

          {/* Answer Image */}
          {answer.Img && (
            <View style={{ marginTop: 4 }}>
              <ClickableImage
                source={{
                  uri: answer.Img?.includes('http')
                    ? answer.Img
                    : ConfigAPI.getValidLink(answer.Img),
                }}
                style={{
                  width: '100%',
                  height: 60,
                  borderRadius: 6,
                }}
                resizeMode="contain"
              />
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  },
);

// Image Container Component with error handling
const ImageContainer = memo(
  ({ imageUrl, questionId }: { imageUrl: string; questionId: string }) => {
    const [imageError, setImageError] = useState(false);
    const [imageLoading, setImageLoading] = useState(true);

    const processedUrl = useMemo(() => {
      const url = ConfigAPI.getValidLink(imageUrl);
      console.log('🖼️ Question Image Debug:');
      console.log('  - Original Img:', imageUrl);
      console.log('  - Processed URL:', url);
      console.log('  - Question ID:', questionId);
      return url;
    }, [imageUrl, questionId]);

    return (
      <View
        style={{
          marginHorizontal: 16,
          marginBottom: 12,
          borderRadius: 8,
          overflow: 'hidden',
          backgroundColor: ColorThemes.light.neutral_main_background_color,
          // Fixed height container to prevent layout shift
          height: 200,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {imageError ? (
          // Error placeholder
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: '#f5f5f5',
            }}>
            <Text
              style={{
                color: '#999',
                fontSize: 14,
                textAlign: 'center',
              }}>
              Không thể tải ảnh
            </Text>
            <Text
              style={{
                color: '#ccc',
                fontSize: 12,
                marginTop: 4,
              }}>
              {questionId}
            </Text>
          </View>
        ) : (
          <>
            {imageLoading && (
              <ActivityIndicator
                size="small"
                color={ColorThemes.light.Primary_Color_Main}
                style={{ position: 'absolute', zIndex: 1 }}
              />
            )}
            <Image
              source={{ uri: processedUrl }}
              style={{
                width: '100%',
                height: '100%',
                borderRadius: 8,
              }}
              resizeMode="contain"
              onLoad={() => {
                console.log('✅ Image loaded successfully:', processedUrl);
                setImageLoading(false);
              }}
              onError={error => {
                console.error('❌ Image load failed:', error);
                console.error('  - URL:', processedUrl);
                setImageError(true);
                setImageLoading(false);
              }}
              onLoadStart={() => {
                console.log('🔄 Image loading started:', processedUrl);
                setImageLoading(true);
                setImageError(false);
              }}
            />
          </>
        )}
      </View>
    );
  },
);

// Memoized Question Component with optimized re-render conditions
const QuestionItem = memo(
  ({
    question,
    questionIndex,
    onAnswerSelect,
    onPlayAudio,
    currentPlayingAudio,
    playedAudios,
    t,
  }: {
    question: any;
    questionIndex: number;
    onAnswerSelect: (questionId: string, answerId: string) => void;
    onPlayAudio: (audioUrl: string, questionId: string) => void;
    currentPlayingAudio: string | null;
    playedAudios: { [key: string]: boolean };
    t: any;
  }) => {
    // Helper function to get consistent audio URL
    const getQuestionAudioUrl = (audioPath: string) => {
      return audioPath?.includes('http')
        ? audioPath
        : ConfigAPI.getValidLink(audioPath);
    };

    const questionAudioUrl = question.Audio
      ? getQuestionAudioUrl(question.Audio)
      : '';

    return (
      <View style={{ marginBottom: 24 }}>
        {/* Question Header */}
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 4,
            paddingVertical: 8,
            paddingHorizontal: 8,
          }}>
          <Text
            style={{
              ...TypoSkin.subtitle2,
              fontWeight: '700',
              color: ColorThemes.light.Neutral_Text_Color_Title,
            }}>
            {`【${questionIndex + 1}】`}
          </Text>
          {/* <Text
            style={{
              ...TypoSkin.body3,
              color: ColorThemes.light.neutral_text_subtitle_color,
            }}>
            ({' '}
            {question.SelectionType === 2
              ? t('exam.selectMultipleAnswers')
              : t('exam.selectOneAnswer')}{' '}
            )
          </Text> */}
          <View style={{ flex: 1, width: '80%' }}>
            <RenderHTML
              contentWidth={Dimensions.get('window').width - 32}
              source={{
                html:
                  question.Title || question.Name || question.Content || ' ',
              }}
              tagsStyles={{
                body: {
                  fontSize: 16,
                  lineHeight: 24,
                  fontFamily: 'Noto Sans JP',
                },
                p: {
                  fontSize: 16,
                  lineHeight: 24,
                  fontFamily: 'Noto Sans JP',
                  color: '#18181B',
                  fontWeight: '600',
                },
                img: {
                  marginVertical: 10,
                  alignSelf: 'center',
                  borderRadius: 8,
                  borderWidth: 1,
                  maxWidth: '100%',
                  height: 200,
                },
                u: {
                  textDecorationLine: 'underline',
                },
              }}
              renderers={{
                img: ({ TDefaultRenderer, ...props }: any) => {
                  const { src } = props.tnode.attributes;
                  if (src) {
                    return (
                      <ClickableImage
                        source={{ uri: src }}
                        style={{
                          marginVertical: 10,
                          alignSelf: 'center',
                          borderRadius: 8,
                          borderWidth: 1,
                          maxWidth: '100%',
                          height: 200,
                        }}
                        resizeMode="contain"
                      />
                    );
                  }
                  return <TDefaultRenderer {...props} />;
                },
              }}
            />
          </View>
        </View>

        {/* Question Content */}

        {/* Question Image (separate field) */}
        {question.Img && (
          <ImageContainer imageUrl={question.Img} questionId={question.Id} />
        )}

        {/* Question Audio (separate field) */}
        {question.Audio && (
          <View
            style={{
              marginBottom: 12,
              paddingHorizontal: 16,
              paddingVertical: 8,
              backgroundColor: ColorThemes.light.neutral_main_background_color,
              borderRadius: 8,
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <TouchableOpacity
              disabled={playedAudios[`question_${question.Id}`]}
              onPress={() => {
                console.log('🎧 TouchableOpacity pressed');
                console.log('🎧 question.Audio exists:', !!question.Audio);
                console.log('🎧 questionAudioUrl:', questionAudioUrl);
                if (
                  question.Audio &&
                  !playedAudios[`question_${question.Id}`]
                ) {
                  console.log('🎧 Calling onPlayAudio with:', questionAudioUrl);
                  onPlayAudio(questionAudioUrl, question.Id);
                } else {
                  console.log('🎧 Audio already played or no audio to play');
                }
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                flex: 1,
                opacity: playedAudios[`question_${question.Id}`] ? 0.5 : 1,
              }}>
              <Winicon
                src={
                  currentPlayingAudio === questionAudioUrl
                    ? 'color/multimedia/button-pause'
                    : 'fill/multimedia/sound'
                }
                size={16}
                color={
                  playedAudios[`question_${question.Id}`]
                    ? ColorThemes.light.Neutral_Text_Color_Subtitle
                    : ColorThemes.light.Info_Color_Main
                }
                style={{ marginRight: 8 }}
              />
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: playedAudios[`question_${question.Id}`]
                    ? ColorThemes.light.Neutral_Text_Color_Subtitle
                    : ColorThemes.light.Info_Color_Main,
                  flex: 1,
                }}>
                {currentPlayingAudio === questionAudioUrl
                  ? 'Đang nghe...'
                  : playedAudios[`question_${question.Id}`]
                    ? 'Đã nghe'
                    : t('exam.listenToQuestion')}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Answers */}
        <View style={{ gap: 8, paddingHorizontal: 16 }}>
          {question.lstAnswer?.map((answer: any, index: number) => (
            <AnswerItem
              key={answer.Id}
              answer={answer}
              index={index}
              questionId={question.Id}
              selectionType={question.SelectionType ?? 1}
              onSelect={onAnswerSelect}
            />
          ))}
        </View>
      </View>
    );
  },
  // Custom comparison function to prevent unnecessary re-renders
  (prevProps, nextProps) => {
    // Only re-render if these specific props change
    return (
      prevProps.question.Id === nextProps.question.Id &&
      prevProps.questionIndex === nextProps.questionIndex &&
      prevProps.currentPlayingAudio === nextProps.currentPlayingAudio &&
      // Check if user answers for this question changed
      JSON.stringify(prevProps.question.lstAnswer) ===
      JSON.stringify(nextProps.question.lstAnswer) &&
      // Check if playedAudios changed for this question
      prevProps.playedAudios[`question_${prevProps.question.Id}`] ===
      nextProps.playedAudios[`question_${nextProps.question.Id}`]
    );
  },
);

export default function DoingTestNewRedesigned() {
  const route = useRoute<any>();
  const { testId, Step, type } = route.params;
  const dispatch: AppDispatch = useDispatch();

  const scrollSectionRef = useRef<any>(null);
  const questionsListRef = useRef<any>(null); // Ref for questions FlatList

  // State management
  const [testData, setTestData] = useState<any>();
  const [sections, setSections] = useState<any[]>([]);
  const [exams, setExams] = useState<any[]>([]);
  const [allQuestions, setAllQuestions] = useState<any[]>([]); // Cache all questions
  const [currentQuestions, setCurrentQuestions] = useState<any[]>([]);
  const [selectedSection, setSelectedSection] = useState<any>(null);
  const [selectedExam, setSelectedExam] = useState<any>(null);
  const [currentExamIndex, setCurrentExamIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const { t } = useTranslation();
  // New states for section-based exam logic
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [submittedSections, setSubmittedSections] = useState<Set<string>>(
    new Set(),
  );
  const [currentSectionTime, setCurrentSectionTime] = useState<number>(0);
  const [sectionTimerKey, setSectionTimerKey] = useState<number>(0); // Force timer reset
  const [pendingNextSection, setPendingNextSection] = useState<any>(null); // Track pending section change
  //navigate
  const navigation = useNavigation<any>();

  // Controllers
  const examController = useMemo(() => new DataController('Exam'), []);
  const testResultController = useMemo(
    () => new DataController('Test_Result'),
    [],
  );
  const answerController = useMemo(() => new DataController('Answer'), []);

  // Use ref to store user answers to prevent race conditions
  const userAnswersRef = useRef<{
    [questionId: string]: { [answerId: string]: boolean };
  }>({});

  // State to store snapshot of user answers for modal re-rendering
  const [userAnswersSnapshot, setUserAnswersSnapshot] = useState<{
    [questionId: string]: { [answerId: string]: boolean };
  }>({});

  // State to force modal re-render
  const [modalKey, setModalKey] = useState(0);

  // Debug function to check current state
  const debugCurrentState = () => {
    console.log('🐛 DEBUG - Current State:');
    console.log(
      '  - userAnswersRef.current:',
      JSON.stringify(userAnswersRef.current, null, 2),
    );
    console.log(
      '  - userAnswersSnapshot:',
      JSON.stringify(userAnswersSnapshot, null, 2),
    );
    console.log('  - currentQuestions length:', currentQuestions.length);
    console.log(
      '  - selectedSection:',
      selectedSection?.Name,
      'ID:',
      selectedSection?.Id,
    );
    console.log('  - allQuestions length:', allQuestions.length);
    console.log(
      '  - allQuestions sample IDs:',
      allQuestions.slice(0, 5).map(q => q.Id),
    );
    console.log('  - exams length:', exams.length);
    console.log(
      '  - exams for current section:',
      exams.filter(exam => exam.SectionId === selectedSection?.Id).length,
    );

    const totalAnswered = Object.keys(userAnswersRef.current).filter(
      questionId => {
        const answers = userAnswersRef.current[questionId];
        return (
          answers && Object.values(answers).some(selected => selected === true)
        );
      },
    ).length;
    console.log('  - Total answered questions:', totalAnswered);
  };

  const bottomSheetRef = useRef<any>(null);

  // State for exam media collapse
  const [showExamImage, setShowExamImage] = useState(true);
  const [currentPlayingAudio, setCurrentPlayingAudio] = useState<string | null>(
    null,
  );
  // State to track played audios (one-time play only)
  const [playedAudios, setPlayedAudios] = useState<{ [key: string]: boolean }>(
    {},
  );

  // State for timer animation
  const [isHalfTimeReached, setIsHalfTimeReached] = useState(false);

  // Callback for when half time is reached
  const handleHalfTimeReached = useCallback(() => {
    console.log('🕐 Half time reached - starting timer animation');
    setIsHalfTimeReached(true);
  }, []);

  // Helper function to apply user answers to questions
  const applyUserAnswersToQuestions = useCallback((questions: any[]) => {
    return questions.map(question => {
      // Safety check for question and lstAnswer
      if (
        !question ||
        !question.lstAnswer ||
        !Array.isArray(question.lstAnswer)
      ) {
        console.log(
          `⚠️ Question ${question?.Id} has no valid lstAnswer:`,
          question?.lstAnswer,
        );
        return question;
      }

      return {
        ...question,
        lstAnswer: question.lstAnswer.map((answer: any) => ({
          ...answer,
          choose: userAnswersRef.current[question.Id]?.[answer.Id] || false,
        })),
      };
    });
  }, []);

  // Helper function to get questions with preserved answers
  // Questions are already randomized once during API call, just preserve order
  const getQuestionsWithAnswers = useCallback(
    (questionIds: string[], examData?: any) => {
      // Get questions in the order they appear in allQuestions (already randomized if needed)
      const questions = questionIds
        .map((questionId: any) => {
          return allQuestions.find((q: any) => q.Id === questionId);
        })
        .filter(Boolean);

      // NO MORE RANDOMIZATION HERE - questions are already randomized once during API call
      // Just preserve the order from allQuestions which contains the final randomized order
      console.log('📌 Using pre-randomized question order for exam:', examData?.Name);

      return applyUserAnswersToQuestions(questions);
    },
    [allQuestions, applyUserAnswersToQuestions],
  );
  const getQuestionDisplayIndex = useCallback(
    (questionId: string, currentIndexInExam?: number) => {
      let displayIndex = 0;

      // Get all sections up to current section
      const targetSectionIndex = sections.findIndex(
        s => s.Id === selectedSection?.Id,
      );

      // Count questions from previous sections
      for (let i = 0; i < targetSectionIndex; i++) {
        const sectionExams = exams.filter(
          exam => exam.SectionId === sections[i].Id,
        );
        const sectionQuestionCount = sectionExams.reduce((total, exam) => {
          const questionIds = exam.QuestionId?.split(',') || [];
          return total + questionIds.length;
        }, 0);
        displayIndex += sectionQuestionCount;
      }

      // Count questions from previous exams in current section
      const currentSectionExams = exams.filter(
        exam => exam.SectionId === selectedSection?.Id,
      );

      // Find which exam this question belongs to
      let targetExamIndex = -1;
      for (let i = 0; i < currentSectionExams.length; i++) {
        const exam = currentSectionExams[i];
        if (exam.Id === selectedExam?.Id) {
          targetExamIndex = i;
          break;
        }
      }

      // Add questions from previous exams in current section
      for (let i = 0; i < targetExamIndex; i++) {
        const exam = currentSectionExams[i];
        const questionIds = exam.QuestionId?.split(',') || [];
        displayIndex += questionIds.length;
      }

      // Add current question index in current exam (0-based from randomized order)
      if (currentIndexInExam !== undefined) {
        displayIndex += currentIndexInExam;
      } else {
        // Fallback: find question position in original exam order (not recommended for randomized)
        const currentExamQuestionIds = selectedExam?.QuestionId?.split(',') || [];
        const questionIndex = currentExamQuestionIds.findIndex(
          (id: string) => id === questionId,
        );
        if (questionIndex !== -1) {
          displayIndex += questionIndex;
        }
      }

      return displayIndex;
    },
    [sections, selectedSection, exams, selectedExam],
  );

  // Load initial test data
  useEffect(() => {
    stopAudio();
    // Reset played audios when test starts
    setPlayedAudios({});
    const getTestData = async () => {
      if (testId) {
        try {
          const res = await examController.getPatternList({
            query: `@TestId:{${testId}}`,
            pattern: {
              TestId: ['Id', 'Name', 'Time', 'Level', 'SectionId', 'Score'],
              SectionId: ['Id', 'Name', 'Time', 'Score'],
              QuestionId: [
                'Id',
                'Name',
                'Title',
                'SelectionType',
                'Score',
                'Audio',
                'Img',
                'Pdf',
                'IsRandom',
                'Sort',
              ],
            },
            // Also get Exam data with IsRandom field
            returns: [
              'Id',
              'Name',
              'TestId',
              'SectionId',
              'QuestionId',
              'Sort',
              'IsRandom', // Add IsRandom field for Exam
              'Audio',
              'Status',
            ],
          });
          if (res.code === 200) {
            setTestData(res.Test[0]);
            const sectionsData = res.Test[0]?.SectionId?.split(',').map(
              (id: any) => res.Section.find((e: any) => e.Id === id),
            );
            //loại bỏ những item undefined
            const filteredSections = sectionsData.filter(Boolean);
            // get all questions in a section and remove duplicate question ids
            const questionsInSections = filteredSections.map((section: any) => {
              const sectionExams = res.data.filter(
                (exam: any) => exam.SectionId === section.Id,
              );
              var questionIds = sectionExams
                .map((exam: any) => exam.QuestionId?.split(','))
                .flat();

              // kiểm tra xem có Id trùng thì loại bỏ
              // questionIds = questionIds.filter((id: any, index: number) => {
              //   return questionIds.indexOf(id) === index;
              // });
              const uniqueQuestionIds = [...questionIds];
              return uniqueQuestionIds;
            });

            // map questions total to filteredSections
            const sectionsWithQuestions = filteredSections.map(
              (section: any, index: number) => ({
                ...section,
                totalQuestions: questionsInSections[index].length,
              }),
            );

            setSections(sectionsWithQuestions);

            const examsData = res.data.sort(
              (a: any, b: any) => a.Sort - b.Sort,
            );
            examsData.map(async (exam: any) => {
              const rs = await BaseDA.getFilesInfor([exam.Audio]);
              if (rs && rs.data && rs.data.length > 0) {
                exam.Audio = rs.data[0].Url;
              }
            });
            setExams(examsData);
            // Store questions temporarily

            // check selectionType == null => 1 chon 1
            if (res.Question) {
              res.Question = res.Question?.map((item: any) => {
                return { ...item, SelectionType: item?.SelectionType || 1 };
              });
            }
            if (sectionsData.length > 0) {
              setSelectedSection(sectionsData[0]);
              setCurrentSectionIndex(0);
              // Set initial section time
              setCurrentSectionTime(sectionsData[0]?.Time || 0);
              setSectionTimerKey(Date.now()); // Force timer reset

              const firstSectionExams = examsData.filter(
                (exam: any) => exam.SectionId === sectionsData[0].Id,
              );
              if (firstSectionExams.length > 0) {
                setSelectedExam(firstSectionExams[0]);
                // Set initial questions (answers will be combined later)
                // Questions are already randomized in the API call above, just get them
                const questionIds = firstSectionExams[0]?.QuestionId?.split(',') || [];
                const initialQuestions = questionIds
                  .map((questionId: any) => {
                    return res.Question.find((q: any) => q.Id === questionId);
                  })
                  .filter(Boolean);
                // setCurrentQuestions(initialQuestions);
                await mappAnswerQuestion(res.Question, initialQuestions, firstSectionExams[0]);
              }
            }
          }
        } catch (error) {
          console.error('Error loading test data:', error);
        } finally {
          setLoading(false);
        }
      }
    };
    getTestData();
  }, [testId]);
  const mappAnswerQuestion = async (
    lstquestion: any,
    initialQuestions: any,
    firstExam?: any,
  ) => {
    if (lstquestion.length > 0) {
      answerController
        .getListSimple({
          query: `@QuestionId:{${lstquestion
            .map((e: any) => e.Id)
            .join(' | ')}}`,
          returns: [
            'Id',
            'Name',
            'Content',
            'Sort',
            'QuestionId',
            'IsResult',
            'Img',
            'Audio',
          ],
          sortby: { BY: 'Sort', DIRECTION: 'ASC' },
        })
        .then(res => {
          if (res.code === 200) {
            console.log(
              '✅ Answers loaded, combining with questions...',
              res.data.length,
            );
            // Combine questions with answers and update allQuestions
            // IMPORTANT: Random answers ONLY ONCE here based on Question IsRandom flag
            // Question randomization will be handled later per exam
            const questionsWithAnswers = lstquestion.map((question: any) => ({
              ...question,
              lstAnswer: !question.IsRandom
                ? res.data
                  .filter((answer: any) => answer.QuestionId === question.Id)
                  .map((answer: any) => ({
                    ...answer,
                    choose: false, // Initialize as not chosen
                  }))
                : shuffleArray(
                  res.data
                    .filter(
                      (answer: any) => answer.QuestionId === question.Id,
                    )
                    .map((answer: any) => ({
                      ...answer,
                      choose: false, // Initialize as not chosen
                    })),
                ),
            }));

            console.log(
              '📋 Questions with answers:',
              questionsWithAnswers[0]?.lstAnswer?.length,
            );
            console.log(
              '📋 Total questionsWithAnswers:',
              questionsWithAnswers.length,
            );
            console.log(
              '📋 Sample question IDs:',
              questionsWithAnswers.slice(0, 3).map((q: any) => q.Id),
            );

            setAllQuestions(questionsWithAnswers);

            // Set currentQuestions using the randomized order from allQuestions
            // Get the first exam and its questions in randomized order
            if (firstExam) {
              console.log('🔍 FirstExam:', firstExam.Name, 'IsRandom:', firstExam.IsRandom);
              const examQuestionIds = firstExam.QuestionId?.split(',') || [];
              console.log('🔍 Exam question IDs:', examQuestionIds);

              // Apply question randomization for this exam if needed
              let examQuestions = examQuestionIds
                .map((questionId: string) => {
                  return questionsWithAnswers.find((q: any) => q.Id === questionId);
                })
                .filter(Boolean);

              // Random questions if this exam has IsRandom = true
              if (firstExam.IsRandom === true) {
                console.log('🎲 Randomizing questions for first exam:', firstExam.Name);
                examQuestions = shuffleArray(examQuestions);
                console.log('🎲 Questions after random:', examQuestions.map((q: any) => q.Id));
              } else {
                console.log('📌 Keeping original order for first exam:', firstExam.Name);
                examQuestions.sort((a: any, b: any) => a.Sort - b.Sort);
              }

              const randomizedCurrentQuestions = examQuestions
                .filter(Boolean)
                .map((question: any) => ({
                  ...question,
                  lstAnswer: question.lstAnswer.map((answer: any) => ({
                    ...answer,
                    choose: userAnswersRef.current[question.Id]?.[answer.Id] || false,
                  })),
                }));

              console.log('📋 Setting randomized currentQuestions:', randomizedCurrentQuestions.length);
              console.log('📋 Question IDs in order:', randomizedCurrentQuestions.map((q: any) => q.Id));
              console.log('📋 First question ID:', randomizedCurrentQuestions[0]?.Id);
              setCurrentQuestions(randomizedCurrentQuestions);
            }
          } else {
            console.log('❌ Failed to load answers:', res);
          }
          setLoading(false);
        })
        .catch(error => {
          console.log('❌ Error loading answers:', error);
        });
    }
  };
  // Handle section selection - Only allow sequential access
  const handleSectionChange = (section: any) => {
    const sectionIndex = sections.findIndex(s => s.Id === section.Id);
    const currentIndex = sections.findIndex(s => s.Id === selectedSection?.Id);

    console.log('🔄 handleSectionChange called:');
    console.log('  - Target section:', section.Name, 'index:', sectionIndex);
    console.log(
      '  - Current section:',
      selectedSection?.Name,
      'index:',
      currentIndex,
    );
    console.log('  - Submitted sections:', Array.from(submittedSections));
    console.log(
      '  - Current section submitted?',
      submittedSections.has(sections[currentIndex]?.Id),
    );

    // Only allow access to current section or next section if current is submitted
    const canAccessNext =
      sectionIndex === currentIndex + 1 &&
      submittedSections.has(sections[currentIndex]?.Id);
    const canAccessCurrent = sectionIndex === currentIndex;

    if (!canAccessCurrent && !canAccessNext) {
      console.log(
        '❌ Can only access current section or next section after submitting current one',
      );
      console.log('  - canAccessCurrent:', canAccessCurrent);
      console.log('  - canAccessNext:', canAccessNext);
      return;
    }

    // Cannot go back to submitted sections
    if (submittedSections.has(section.Id)) {
      console.log('❌ Cannot return to submitted section');
      return;
    }

    console.log(`🔄 Changing to section: ${section.Name}`);
    setSelectedSection(section);
    setCurrentSectionIndex(sectionIndex);

    // Set section time and reset timer
    setCurrentSectionTime(section?.Time || 0);
    setSectionTimerKey(Date.now());
    setIsHalfTimeReached(false);

    // scrollSectionRef scroll to center with index
    // Calculate proper scroll position based on section tab widths
    setTimeout(() => {
      if (scrollSectionRef.current && sectionIndex >= 0) {
        // Calculate cumulative width of previous tabs to scroll to the correct position
        let scrollX = 0;
        for (let i = 0; i < sectionIndex; i++) {
          // Estimate each tab width based on section name length + padding
          const sectionName = sections[i]?.Name || '';
          const totalQuestions = sections[i]?.totalQuestions || 0;
          const tabText = `${sectionName} (${totalQuestions})`;
          // Rough calculation: 8px per character + 24px padding + 16px margin
          const estimatedWidth = Math.max(80, tabText.length * 8 + 40);
          scrollX += estimatedWidth;
        }

        console.log(
          `📍 Scrolling section tabs to position: ${scrollX} for section index: ${sectionIndex}`,
        );

        scrollSectionRef.current.scrollTo({
          x: scrollX,
          animated: true,
        });
      }
    }, 300);

    const sectionExams = exams.filter(
      (exam: any) => exam.SectionId === section.Id,
    );
    if (sectionExams.length > 0) {
      setSelectedExam(sectionExams[0]);
      setCurrentExamIndex(0);
      const questionIds = sectionExams[0]?.QuestionId?.split(',');
      const examQuestions = getQuestionsWithAnswers(questionIds, sectionExams[0]);
      console.log(
        `📋 Setting ${examQuestions.length} questions for exam ${sectionExams[0].Name}`,
      );
      setCurrentQuestions(examQuestions);
      // Auto scroll to top when changing section
      setTimeout(() => {
        questionsListRef.current?.scrollToOffset({
          offset: 0,
          animated: true,
        });
      }, 100);
    }
  };

  // Handle exam navigation - No loading, instant switch
  const handleNextExam = () => {
    const sectionExams = exams.filter(
      (exam: any) => exam.SectionId === selectedSection.Id,
    );
    if (currentExamIndex < sectionExams.length - 1) {
      const nextExam = sectionExams[currentExamIndex + 1];
      setSelectedExam(nextExam);
      setCurrentExamIndex(currentExamIndex + 1);
      // Reset timer animation state when changing exams
      setIsHalfTimeReached(false);
      const questionIds = nextExam?.QuestionId?.split(',');
      const examQuestions = getQuestionsWithAnswers(questionIds, nextExam);
      setCurrentQuestions(examQuestions);
      stopAudio();
      // Auto scroll to top when changing exam
      setTimeout(() => {
        questionsListRef.current?.scrollToOffset({
          offset: 0,
          animated: true,
        });
      }, 100);
    }
  };

  const handlePreviousExam = () => {
    const sectionExams = exams.filter(
      (exam: any) => exam.SectionId === selectedSection.Id,
    );
    if (currentExamIndex > 0) {
      const prevExam = sectionExams[currentExamIndex - 1];
      setSelectedExam(prevExam);
      setCurrentExamIndex(currentExamIndex - 1);
      // Reset timer animation state when changing exams
      setIsHalfTimeReached(false);
      const questionIds = prevExam?.QuestionId?.split(',');
      const examQuestions = getQuestionsWithAnswers(questionIds, prevExam);
      setCurrentQuestions(examQuestions);
      stopAudio();
      // Auto scroll to top when changing exam
      setTimeout(() => {
        questionsListRef.current?.scrollToOffset({
          offset: 0,
          animated: true,
        });
      }, 100);
    }
  };

  // Handle direct exam navigation by clicking on page number
  const handleExamPageClick = (examIndex: number) => {
    const sectionExams = exams.filter(
      (exam: any) => exam.SectionId === selectedSection.Id,
    );

    if (
      examIndex >= 0 &&
      examIndex < sectionExams.length &&
      examIndex !== currentExamIndex
    ) {
      const targetExam = sectionExams[examIndex];
      setSelectedExam(targetExam);
      setCurrentExamIndex(examIndex);
      // Reset timer animation state when changing exams
      setIsHalfTimeReached(false);
      const questionIds = targetExam?.QuestionId?.split(',');
      const examQuestions = getQuestionsWithAnswers(questionIds, targetExam);
      setCurrentQuestions(examQuestions);
      stopAudio();
      // Auto scroll to top when changing exam
      setTimeout(() => {
        questionsListRef.current?.scrollToOffset({
          offset: 0,
          animated: true,
        });
      }, 100);
    }
  };

  // Handle answer selection - Preserve answers across sections/exams
  const handleAnswerSelect = useCallback(
    (questionId: string, answerId: string) => {
      console.log(`🎯 Selecting answer ${answerId} for question ${questionId}`);

      // Find the question to get SelectionType
      const question = currentQuestions.find(q => q.Id === questionId);
      if (!question) {
        console.log(`❌ Question ${questionId} not found`);
        return;
      }

      console.log(`📋 Question SelectionType: ${question.SelectionType}`);

      // Update userAnswersRef first
      if (!userAnswersRef.current[questionId]) {
        userAnswersRef.current[questionId] = {};
      }

      if (question.SelectionType === 1) {
        // Single choice - clear all answers for this question first
        console.log('🔘 Single choice - clearing previous answers');
        userAnswersRef.current[questionId] = {};
        userAnswersRef.current[questionId][answerId] = true;
      } else {
        // Multiple choice - toggle this answer
        console.log('☑️ Multiple choice - toggling answer');
        const currentValue =
          userAnswersRef.current[questionId][answerId] || false;
        userAnswersRef.current[questionId][answerId] = !currentValue;
      }

      console.log(
        '💾 Saved to userAnswersRef:',
        userAnswersRef.current[questionId],
      );

      // Update snapshot to trigger re-render of modal
      setUserAnswersSnapshot({ ...userAnswersRef.current });

      // Update currentQuestions with new answers
      setCurrentQuestions(prevQuestions => {
        const updatedQuestions = prevQuestions.map(q => {
          if (q.Id === questionId) {
            return {
              ...q,
              lstAnswer: q.lstAnswer.map((answer: any) => ({
                ...answer,
                choose:
                  userAnswersRef.current[questionId]?.[answer.Id] || false,
              })),
            };
          }
          return q;
        });

        console.log(`✅ Updated currentQuestions for question ${questionId}`);
        return updatedQuestions;
      });

      // Also dispatch to Redux for compatibility (but we rely on local state)
      dispatch(ExamActions.choose(questionId, answerId));
    },
    [dispatch, currentQuestions],
  );

  // Function to handle section submission
  const handleSectionSubmit = () => {
    if (!selectedSection) return;
    stopAudio();

    // Debug current state before showing modal
    debugCurrentState();

    // Update snapshot and modal key to force re-render
    setUserAnswersSnapshot({ ...userAnswersRef.current });
    setModalKey(prev => prev + 1);
    console.log(
      '📊 handleSectionSubmit - Updated userAnswersSnapshot and modalKey',
    );
    const getAllSectionQuestions = () => {
      // Get all questions from previous sections to calculate starting number
      let startingNumber = 1;
      for (let i = 0; i < sections.length; i++) {
        if (sections[i].Id === selectedSection.Id) {
          break;
        }
        // Count questions in previous sections
        const prevSectionExams = exams.filter(
          (exam: any) => exam.SectionId === sections[i].Id,
        );
        const prevSectionQuestionCount = prevSectionExams.reduce(
          (total, exam) => {
            const questionIds = exam.QuestionId?.split(',') || [];
            return total + questionIds.length;
          },
          0,
        );
        startingNumber += prevSectionQuestionCount;
      }
      //lấy danh sách question id trong sectionExams
      const sectionExams = exams.filter(
        (exam: any) => exam.SectionId === selectedSection.Id,
      );
      const questionIds = sectionExams
        .map((exam: any) => exam.QuestionId?.split(','))
        .flat();
      const uniqueQuestionIds = [...new Set(questionIds)];
      // Try to get questions from allQuestions first, then from currentQuestions as fallback
      let uniqueQuestions = uniqueQuestionIds
        .map((id: any) => allQuestions.find((q: any) => q.Id === id))
        .filter(Boolean); // Remove undefined questions

      // If no questions found in allQuestions, try to get from currentQuestions
      if (uniqueQuestions.length === 0 && currentQuestions.length > 0) {
        console.log(
          '🔄 Fallback: Using currentQuestions since allQuestions is empty for this section',
        );
        uniqueQuestions = uniqueQuestionIds
          .map((id: any) => currentQuestions.find((q: any) => q.Id === id))
          .filter(Boolean);
      }

      console.log(
        '🔍 getAllSectionQuestions - section.Id:',
        selectedSection.Id,
      );
      console.log(
        '🔍 getAllSectionQuestions - sectionExams:',
        sectionExams.length,
      );
      console.log(
        '🔍 getAllSectionQuestions - questionIds from exams:',
        questionIds,
      );
      console.log(
        '🔍 getAllSectionQuestions - uniqueQuestionIds:',
        uniqueQuestionIds,
      );
      console.log(
        '🔍 getAllSectionQuestions - allQuestions.length:',
        allQuestions.length,
      );
      console.log(
        '🔍 getAllSectionQuestions - allQuestions sample IDs:',
        allQuestions.slice(0, 3).map(q => q.Id),
      );
      console.log(
        '🔍 getAllSectionQuestions - uniqueQuestions:',
        uniqueQuestions.length,
      );
      console.log(
        '🔍 getAllSectionQuestions - sample question:',
        uniqueQuestions[0],
      );

      // Don't apply user answers here - we'll check userAnswersRef directly in the UI
      var allSectionQuest = uniqueQuestions.map((a: any, index: number) => ({
        ...a,
        exam: sectionExams.find((exam: any) =>
          exam.QuestionId?.split(',').includes(a?.Id),
        ),
        displayNumber: startingNumber + index,
      }));

      return allSectionQuest;
    };
    const allSectionQuestions = getAllSectionQuestions();
    // Show section overview modal
    showBottomSheet({
      ref: bottomSheetRef,
      enableDismiss: true,

      children: (
        <SectionOverview
          key={modalKey}
          ref={bottomSheetRef}
          section={selectedSection}
          allSectionQuestions={allSectionQuestions}
        />
      ),
    });
  };

  // Function to confirm section submission
  const confirmSectionSubmit = useCallback(() => {
    if (!selectedSection) return;

    console.log('🔄 Confirming section submit for:', selectedSection.Name);

    // Check if this is the last section
    const currentIndex = sections.findIndex(s => s.Id === selectedSection.Id);
    const isLastSection = currentIndex === sections.length - 1;

    console.log(
      '📍 Current section index:',
      currentIndex,
      'Is last section:',
      isLastSection,
    );

    // Mark section as submitted
    setSubmittedSections(prev => {
      const newSet = new Set([...prev, selectedSection.Id]);
      console.log('✅ Updated submitted sections:', Array.from(newSet));
      return newSet;
    });

    if (isLastSection) {
      // Last section - calculate final score and navigate to results
      console.log('🏁 Last section - calculating final results');
      const examResult = createDetailedResultData();
      submitFinalExam(examResult);
    } else {
      // Not last section - set pending next section
      const nextSection = sections[currentIndex + 1];
      console.log('➡️ Setting pending next section:', nextSection?.Name);
      if (nextSection) {
        setPendingNextSection(nextSection);
      }
    }
  }, [selectedSection, sections]);

  // Effect to handle section change after submission
  useEffect(() => {
    if (pendingNextSection) {
      console.log(
        '🔄 Processing pending section change to:',
        pendingNextSection.Name,
      );
      handleSectionChange(pendingNextSection);
      setPendingNextSection(null);
    }
  }, [submittedSections, pendingNextSection]);

  // Function to jump to specific question
  const jumpToQuestion = useCallback(
    (targetQuestionId: string, targetExam: any) => {
      console.log(
        '🎯 Jumping to question:',
        targetQuestionId,
        'in exam:',
        targetExam.Name,
      );

      // First, switch to the target exam if it's different from current exam
      const sectionExams = exams.filter(
        (exam: any) => exam.SectionId === selectedSection?.Id,
      );
      const targetExamIndex = sectionExams.findIndex(
        (exam: any) => exam.Id === targetExam.Id,
      );

      if (targetExamIndex !== currentExamIndex) {
        console.log('🔄 Switching to exam index:', targetExamIndex);
        setSelectedExam(targetExam);
        setCurrentExamIndex(targetExamIndex);
        // Reset timer animation state when changing exams
        setIsHalfTimeReached(false);

        // Load questions for target exam with answers
        const questionIds = targetExam.QuestionId?.split(',') || [];

        const examQuestions = getQuestionsWithAnswers(questionIds, targetExam);
        setCurrentQuestions(examQuestions);
        stopAudio();

        // Wait for questions to load, then scroll
        setTimeout(() => {
          const questionIndex = examQuestions.findIndex(
            (q: any) => q.Id === targetQuestionId,
          );
          if (questionIndex !== -1 && questionsListRef.current) {
            console.log('📍 Scrolling to question index:', questionIndex);
            questionsListRef.current.scrollToIndex({
              index: questionIndex,
              animated: true,
              viewPosition: 0.1, // Show question at top 10% of screen
            });
          }
        }, 300);
      } else {
        // Same exam, just scroll to question
        const questionIndex = currentQuestions.findIndex(
          (q: any) => q.Id === targetQuestionId,
        );
        if (questionIndex !== -1 && questionsListRef.current) {
          console.log('📍 Scrolling to question index:', questionIndex);
          questionsListRef.current.scrollToIndex({
            index: questionIndex,
            animated: true,
            viewPosition: 0.1, // Show question at top 10% of screen
          });
        }
      }

      // Close modal
      hideBottomSheet(bottomSheetRef);
    },
    [
      selectedSection,
      exams,
      currentExamIndex,
      currentQuestions,
      allQuestions,
      getQuestionsWithAnswers,
    ],
  );

  // Function to submit final exam results
  const submitFinalExam = useCallback(
    async (examResult: any) => {
      try {
        console.log('📊 Final Exam Result:', examResult);
        const customer = store.getState().customer.data;
        const testResultData = {
          Id: randomGID(),
          TestId: testId,
          CustomerId: customer?.Id,
          QuestionId: examResult.questionIds,
          AnswerId: examResult.answerIds,
          Score: examResult.totalScore,
          DateCreated: new Date().getTime(),
          Name: customer?.Name + ' ' + testData.Name,
          Status: examResult.isPassed ? StatusExam.passed : StatusExam.fail,
        };

        if (examResult.isPassed && type === 'Test' && Step) {
          dispatch(
            LessonActions.updateProcess({
              Id: Step?.lessonId,
              CourseId: Step?.courseId,
              lessonId: Step?.lessonId,
              stepOrder: Step?.StepOrder,
              PartId: Step?.PartId,
              PercentCompleted: 100,
              Type: 'Test',
            }),
          );
          dispatch(
            CustomerActions.updateRank(SakupiType.tryTest, Sakupi.tryTest),
          );
        }

        const result = await testResultController.add([testResultData]);
        if (result.code === 200) {
          navigation.replace(RootScreen.resultTestNew, {
            item: examResult,
          });
        }
      } catch (error) {
        console.error('❌ Error submitting final exam:', error);
        Alert.alert('Error', 'Error submitting exam. Please try again.');
      }
    },
    [testId, testData, type, Step, dispatch, testResultController, navigation],
  );

  // Function to collect all answers and calculate score with pass/fail logic
  const collectAnswersForSubmit = useCallback(() => {
    console.log('🔍 collectAnswersForSubmit called');
    console.log('📊 userAnswersRef.current:', userAnswersRef.current);
    console.log('📊 allQuestions length:', allQuestions.length);

    const answeredQuestions: string[] = [];
    const selectedAnswers: string[] = [];
    let totalScore = 0;
    let correctAnswers = 0;

    // Calculate score by section for elimination check
    const sectionScores: {
      [sectionId: string]: {
        score: number;
        maxScore: number;
        eliminationScore: number;
        sectionName: string;
      };
    } = {};

    // Initialize section scores
    sections.forEach((section: any) => {
      sectionScores[section.Id] = {
        score: 0,
        maxScore: 0,
        eliminationScore: section.Score || 0, // Điểm liệt của section
        sectionName: section.Name,
      };
    });

    // Loop through all questions to collect answers and calculate scores
    allQuestions.forEach((question: any) => {
      // Find which section this question belongs to
      const questionExam = exams?.find(
        (exam: any) =>
          exam.QuestionId && exam?.QuestionId?.split(',').includes(question.Id),
      );
      const sectionId = questionExam?.SectionId;

      if (sectionId && sectionScores[sectionId]) {
        // Add to max score for this section
        sectionScores[sectionId].maxScore += question.Score || 1;
      }

      const userAnswers = userAnswersRef.current[question.Id];
      console.log(`🔍 Question ${question.Id} userAnswers:`, userAnswers);

      if (userAnswers && Object.keys(userAnswers).length > 0) {
        // Get selected answer IDs for this question
        const selectedAnswerIds = Object.keys(userAnswers).filter(
          answerId => userAnswers[answerId] === true,
        );

        console.log(
          `✅ Question ${question.Id} selectedAnswerIds:`,
          selectedAnswerIds,
        );

        if (selectedAnswerIds.length > 0) {
          answeredQuestions.push(question.Id);
          selectedAnswers.push(...selectedAnswerIds);

          // Calculate score for this question
          const correctAnswerIds =
            question.lstAnswer
              ?.filter(
                (answer: any) =>
                  answer.IsResult === true ||
                  answer.IsResult === 1 ||
                  answer.IsResult === '1',
              )
              ?.map((answer: any) => answer.Id) || [];

          // Check if user's answers match correct answers
          const isCorrect =
            question.SelectionType === 1
              ? selectedAnswerIds.length === 1 &&
              correctAnswerIds.includes(selectedAnswerIds[0])
              : selectedAnswerIds.length === correctAnswerIds.length &&
              selectedAnswerIds.every((id: string) =>
                correctAnswerIds.includes(id),
              ) &&
              correctAnswerIds.every((id: string) =>
                selectedAnswerIds.includes(id),
              );
          if (isCorrect) {
            const questionScore = question.Score || 1;
            totalScore += questionScore;
            correctAnswers++;

            // Add score to section
            if (sectionId && sectionScores[sectionId]) {
              sectionScores[sectionId].score += questionScore;
            }
          }
        }
      }
    });

    // Check pass/fail logic
    let isPassed = true;
    let failReason = '';
    const sectionResults: any[] = [];

    // 1. Check elimination scores for each section
    Object.keys(sectionScores).forEach(sectionId => {
      const sectionData = sectionScores[sectionId];
      const isEliminationPassed =
        sectionData.score >= sectionData.eliminationScore;

      sectionResults.push({
        sectionId,
        sectionName: sectionData.sectionName,
        score: sectionData.score,
        maxScore: sectionData.maxScore,
        eliminationScore: sectionData.eliminationScore,
        isPassed: isEliminationPassed,
      });

      if (!isEliminationPassed && sectionData.eliminationScore > 0) {
        isPassed = false;
        failReason = `Điểm liệt section "${sectionData.sectionName}": ${sectionData.score}/${sectionData.eliminationScore}`;
      }
    });

    // 2. If no elimination, check overall pass score
    if (isPassed) {
      const passScore = testData?.Score || 0; // Điểm đạt của test
      if (totalScore < passScore && passScore > 0) {
        isPassed = false;
        failReason = `Điểm tổng không đạt: ${totalScore}/${passScore}`;
      }
    }

    console.log('📊 Section Results:', sectionResults);
    console.log(
      `🎯 Pass/Fail: ${isPassed ? 'PASS' : 'FAIL'} - ${failReason || 'Đạt yêu cầu'
      }`,
    );

    console.log('📊 Final Results Summary:');
    console.log('  - answeredQuestions:', answeredQuestions);
    console.log('  - selectedAnswers:', selectedAnswers);
    console.log('  - answeredCount:', answeredQuestions.length);
    console.log('  - totalQuestions:', allQuestions.length);

    return {
      questionIds: answeredQuestions.join(','),
      answerIds: selectedAnswers.join(','),
      totalScore,
      correctAnswers,
      totalQuestions: allQuestions.length,
      answeredCount: answeredQuestions.length,
      isPassed,
      failReason,
      sectionResults,
      passScore: testData?.Score || 0,
    };
  }, [allQuestions, sections, exams, testData]);

  // Component for section overview modal
  const SectionOverview = useCallback(
    ({
      section,
      ref: modalRef,
      allSectionQuestions,
    }: {
      section: any;
      ref: any;
      allSectionQuestions: any[];
    }) => {
      console.log('🔄 SectionOverview render - section:', section.Name);
      console.log(
        '🔄 SectionOverview render - userAnswersRef.current:',
        JSON.stringify(userAnswersRef.current, null, 2),
      );
      console.log(
        '🔄 SectionOverview render - userAnswersSnapshot:',
        JSON.stringify(userAnswersSnapshot, null, 2),
      );

      // Get exams for current section

      const handleConfirmSubmit = () => {
        hideBottomSheet(modalRef);
        setTimeout(() => {
          confirmSectionSubmit();
        }, 100);
      };
      // Calculate total stats using the new question list
      const totalQuestions = allSectionQuestions?.length;

      console.log('🔍 SectionOverview - Checking answered questions:');
      console.log(
        '📋 allSectionQuestions:',
        allSectionQuestions.map(q => q.Id),
      );
      console.log(
        '💾 userAnswersRef.current:',
        JSON.stringify(userAnswersRef.current, null, 2),
      );

      const answeredCount = allSectionQuestions.filter((questionData: any) => {
        // Safety check for questionData
        if (!questionData || !questionData.Id) {
          console.log('⚠️ Invalid questionData:', questionData);
          return false;
        }
        const userAnswers = userAnswersRef.current[questionData.Id];
        const isAnswered =
          userAnswers &&
          Object.values(userAnswers).some(selected => selected === true);

        console.log(`📝 Question ${questionData.Id}:`);
        console.log(`   - userAnswers: ${JSON.stringify(userAnswers)}`);
        console.log(`   - isAnswered: ${isAnswered}`);

        return isAnswered;
      }).length;

      console.log(
        `✅ Total answered questions: ${answeredCount}/${allSectionQuestions.length}`,
      );

      return (
        <View
          style={{
            height: Dimensions.get('window').height / 2,
            width: '100%',
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
            paddingHorizontal: 16,
          }}>
          {/*  get header in here */}
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingVertical: 8,
            }}>
            <View style={{ width: 24 }} />
            <Text
              style={{
                ...TypoSkin.title3,
                color: ColorThemes.light.Neutral_Text_Color_Title,
                textAlign: 'center',
                flex: 1,
              }}>
              {section.Name}
            </Text>
            {/* back function */}
            <TouchableOpacity
              onPress={() => hideBottomSheet(modalRef)}
              style={{ padding: 6, alignItems: 'center' }}>
              <Winicon
                src="outline/layout/xmark"
                size={20}
                color={ColorThemes.light.Neutral_Text_Color_Body}
              />
            </TouchableOpacity>
          </View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              paddingTop: 16,
              paddingVertical: 8,
              marginBottom: 16,
              paddingHorizontal: 12,
              borderRadius: 8,
            }}>
            <Text
              style={{
                ...TypoSkin.buttonText5,
                color: ColorThemes.light.Success_Color_Main,
              }}>
              ✅ Đã trả lời: {answeredCount ?? 0}
            </Text>
            <Text
              style={{
                ...TypoSkin.buttonText5,
                color: ColorThemes.light.Error_Color_Main,
              }}>
              ❌ Chưa trả lời: {(totalQuestions ?? 0) - (answeredCount ?? 0)}
            </Text>
            <Text
              style={{
                ...TypoSkin.buttonText5,
                color: ColorThemes.light.Neutral_Text_Color_Body,
              }}>
              📝 Tổng: {totalQuestions ?? 0}
            </Text>
          </View>

          <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                flexWrap: 'wrap',
                gap: 8,
                paddingBottom: 16,
              }}>
              {allSectionQuestions &&
                allSectionQuestions?.map((questionData: any) => {
                  // Safety check for questionData
                  if (!questionData || !questionData.Id) {
                    console.log(
                      '⚠️ Invalid questionData in map:',
                      questionData,
                    );
                    return null;
                  }

                  // Check if question is answered - use simple logic like before
                  const userAnswers = userAnswersRef.current[questionData.Id];
                  const isAnswered =
                    userAnswers &&
                    Object.values(userAnswers).some(
                      selected => selected === true,
                    );

                  console.log(
                    `🎯 Question ${questionData.displayNumber} (${questionData.Id}):`,
                  );
                  console.log(
                    `   - userAnswers: ${JSON.stringify(userAnswers)}`,
                  );
                  console.log(`   - isAnswered: ${isAnswered}`);

                  return (
                    <TouchableOpacity
                      key={questionData.Id}
                      onPress={() =>
                        jumpToQuestion(questionData.Id, questionData.exam)
                      }
                      style={{
                        width: 32,
                        height: 32,
                        borderRadius: 16,
                        backgroundColor: isAnswered
                          ? ColorThemes.light.Warning_Color_Background
                          : ColorThemes.light.transparent,
                        borderWidth: 1,
                        borderColor: isAnswered
                          ? ColorThemes.light.Warning_Color_Main
                          : ColorThemes.light.Neutral_Border_Color_Main,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <Text
                        style={{
                          ...TypoSkin.body3,
                          color: isAnswered
                            ? ColorThemes.light.Warning_Color_Main
                            : ColorThemes.light.Neutral_Text_Color_Body,
                          fontWeight: '600',
                        }}>
                        {questionData.displayNumber}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
            </View>
          </ScrollView>

          <View style={{ paddingTop: 16, gap: 12 }}>
            <AppButton
              title={t('exam.submitSection')}
              backgroundColor={ColorThemes.light.Primary_Color_Main}
              borderColor="transparent"
              containerStyle={{
                height: 45,
                borderRadius: 8,
              }}
              onPress={handleConfirmSubmit}
              textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
            />
            <AppButton
              title={t('exam.continueExam')}
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              containerStyle={{
                height: 45,
                borderRadius: 8,
                marginBottom: 16,
              }}
              onPress={() => {
                hideBottomSheet(modalRef);
              }}
              textColor={ColorThemes.light.Neutral_Text_Color_Body}
            />
          </View>
        </View>
      );
    },
    [
      exams,
      allQuestions,
      currentQuestions,
      userAnswersRef,
      confirmSectionSubmit,
      jumpToQuestion,
      t,
      sections,
      userAnswersSnapshot, // This will trigger re-render when user answers change
    ],
  );

  // Function to create detailed result data for results screen
  const createDetailedResultData = useCallback(() => {
    const basicResult = collectAnswersForSubmit();

    // Create detailed sections with exams and questions
    const detailedSections = sections.map((section: any) => {
      // Get exams for this section
      const sectionExams = exams.filter(
        (exam: any) => exam.SectionId === section.Id,
      );

      // Calculate section statistics
      let sectionScore = 0;
      let sectionMaxScore = 0;
      let sectionCorrectQuestions = 0;
      let sectionTotalQuestions = 0;
      let sectionAnsweredQuestions = 0;

      // Create detailed exams
      const detailedExams = sectionExams
        .map((exam: any) => {
          const examQuestionIds = exam.QuestionId?.split(',') || [];

          // Get questions for this exam
          const examQuestions = examQuestionIds
            .map((questionId: string) => {
              const question = allQuestions.find(
                (q: any) => q.Id === questionId,
              );
              if (!question) return null;

              const userAnswers = userAnswersRef.current[questionId] || {};
              const selectedAnswerIds = Object.keys(userAnswers).filter(
                answerId => userAnswers[answerId] === true,
              );

              // Get correct answers
              const correctAnswerIds =
                question.lstAnswer
                  ?.filter(
                    (answer: any) =>
                      answer.IsResult === true ||
                      answer.IsResult === 1 ||
                      answer.IsResult === '1',
                  )
                  ?.map((answer: any) => answer.Id) || [];

              // Check if question is answered and correct
              const isAnswered = selectedAnswerIds.length > 0;
              const isCorrect =
                isAnswered &&
                (question.SelectionType === 1
                  ? selectedAnswerIds.length === 1 &&
                  correctAnswerIds.includes(selectedAnswerIds[0])
                  : selectedAnswerIds.length === correctAnswerIds.length &&
                  selectedAnswerIds.every((id: string) =>
                    correctAnswerIds.includes(id),
                  ) &&
                  correctAnswerIds.every((id: string) =>
                    selectedAnswerIds.includes(id),
                  ));

              // Update section statistics
              sectionTotalQuestions++;
              sectionMaxScore += question.Score || 1;
              if (isAnswered) sectionAnsweredQuestions++;
              if (isCorrect) {
                sectionCorrectQuestions++;
                sectionScore += question.Score || 1;
              }

              return {
                id: question.Id,
                // content: question.Content || question.Name || 'No content',
                // selectionType: question.SelectionType,
                // score: question.Score || 1,
                // isAnswered,
                isCorrect,
              };
            })
            .filter(Boolean);

          return {
            id: exam.Id,
            name: exam.Name || `Exam ${exam.Sort || 1}`,
            sort: exam.Sort || 1,
            // totalQuestions: examTotalQuestions,
            // answeredQuestions: examAnsweredQuestions,
            // correctQuestions: examCorrectQuestions,
            // score: examScore,
            // maxScore: examMaxScore,
            questions: examQuestions,
          };
        })
        .sort((a: any, b: any) => a.sort - b.sort);

      // Find section result from basic result
      const sectionResult = basicResult.sectionResults.find(
        (sr: any) => sr.sectionId === section.Id,
      );

      return {
        id: section.Id,
        name: section.Name || `Section ${section.Sort || 1}`,
        score: sectionScore,
        maxScore: sectionMaxScore,
        eliminationScore: section.Score || 0,
        isPassed: sectionResult?.isPassed || false,
        totalQuestions: sectionTotalQuestions,
        answeredQuestions: sectionAnsweredQuestions,
        correctQuestions: sectionCorrectQuestions,
        exams: detailedExams,
      };
    });

    // Create final result object
    const detailedResult = {
      // Basic exam info
      testId: testId,
      testName: testData?.Name || 'Exam',
      completedAt: new Date().toISOString(),

      // Overall scores
      totalScore: basicResult.totalScore,
      maxTotalScore: detailedSections.reduce(
        (sum: number, section: any) => sum + section.maxScore,
        0,
      ),
      passScore: basicResult.passScore,
      isPassed: basicResult.isPassed,
      failReason: basicResult.failReason,
      answeredCount: basicResult.answeredCount,
      // Question statistics
      totalQuestions: basicResult.totalQuestions,
      answeredQuestions: basicResult.answeredCount,
      correctAnswers: basicResult.correctAnswers,

      // For API submission
      questionIds: basicResult.questionIds,
      answerIds: basicResult.answerIds,

      // Detailed breakdown for results screen
      sections: detailedSections,
    };

    console.log('📊 Detailed Result Data:', detailedResult);
    return detailedResult;
  }, [
    collectAnswersForSubmit,
    sections,
    exams,
    allQuestions,
    testData,
    testId,
    userAnswersRef,
  ]);

  // Sound reference
  const soundRef = useRef<Sound | null>(null);

  // Helper function to get audio URL
  const getAudioUrl = (audioPath: string) => {
    return ConfigAPI.getValidLink(audioPath);
  };

  // Function to play audio (one-time play only)
  const playAudio = (audioUrl: string, audioKey: string) => {
    console.log('🎵 playAudio called with:', audioUrl, 'key:', audioKey);
    console.log('🎵 Current playedAudios:', playedAudios);
    console.log('🎵 Current playing audio:', currentPlayingAudio);

    // Check if this audio has already been played
    if (playedAudios[audioKey]) {
      console.log('🎵 Audio already played, ignoring request');
      return;
    }

    // Stop any currently playing sound first
    console.log('🎵 Stopping any current audio...');
    stopAudio();

    // Mark this audio as played immediately when user clicks (before actually playing)
    console.log('🎵 Marking audio as played:', audioKey);
    setPlayedAudios(prev => {
      const newState = {
        ...prev,
        [audioKey]: true,
      };
      console.log('🎵 New playedAudios state:', newState);
      return newState;
    });

    // Set current playing audio
    console.log('🎵 Setting current playing audio:', audioUrl);
    setCurrentPlayingAudio(audioUrl);

    // Set up sound
    Sound.setCategory('Playback');

    // Create new sound instance
    const sound = new Sound(audioUrl, '', error => {
      if (error) {
        console.log('❌ Failed to load sound', error);
        setCurrentPlayingAudio(null);
        return;
      }

      console.log('✅ Sound loaded successfully, starting playback...');
      // Play the sound
      sound.play(success => {
        if (success) {
          console.log('✅ Sound played successfully');
        } else {
          console.log('❌ Sound playback failed');
        }
        // Reset playing state when audio finishes
        console.log('🎵 Audio finished, resetting current playing audio');
        setCurrentPlayingAudio(null);
      });
    });

    // Save reference to sound
    soundRef.current = sound;
  };

  // Function to stop audio
  const stopAudio = () => {
    console.log('🛑 stopAudio called');
    console.log('🛑 soundRef.current exists:', !!soundRef.current);
    console.log('🛑 currentPlayingAudio:', currentPlayingAudio);

    if (soundRef.current) {
      console.log('🛑 Stopping and releasing current sound...');
      soundRef.current.stop();
      soundRef.current.release();
      soundRef.current = null;
    }
    console.log('🛑 Resetting currentPlayingAudio to null');
    setCurrentPlayingAudio(null);
  };

  useEffect(() => {
    // Cleanup function to release sound resources when component unmounts
    return stopAudio();
  }, []);

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FLoading
        visible={loading}
        avt={require('../../../assets/appstore.png')}
      />

      {/* Header with timer and submit button */}
      <ScreenHeader
        style={{ paddingTop: Platform.OS === 'ios' ? 0 : 24 }}
        action={
          <AppButton
            title={t('exam.submit')}
            containerStyle={{
              justifyContent: 'flex-start',
              alignSelf: 'baseline',
              paddingRight: 16,
            }}
            backgroundColor={'transparent'}
            textStyle={{
              ...TypoSkin.buttonText2,
              color: ColorThemes.light.Primary_Color_Main,
            }}
            borderColor="transparent"
            onPress={() => {
              handleSectionSubmit();
            }}
            textColor={ColorThemes.light.infor_main_color}
          />
        }
        title={
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              paddingBottom: 8,
            }}>
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 24,
                paddingVertical: 8,
              }}
              onPress={() => {
                handleSectionSubmit();
              }}>
              <Text
                style={{
                  ...TypoSkin.title3,
                  fontWeight: '700',
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                }}>
                {(() => {
                  const answeredCount = Object.keys(
                    userAnswersRef.current,
                  ).filter(questionId => {
                    const answers = userAnswersRef.current[questionId];
                    return (
                      answers &&
                      Object.values(answers).some(selected => selected === true)
                    );
                  }).length;
                  return `${answeredCount}/${exams
                    .map((exam: any) => exam.QuestionId?.split(',').length || 0)
                    .reduce((acc, curr) => acc + curr, 0)}`;
                })()}
              </Text>
            </TouchableOpacity>
          </View>
        }
        backIcon={
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
            <Winicon src="outline/arrows/left-arrow" size={20} />
            <AnimatedTimerIcon
              shouldRotate={isHalfTimeReached}
              source={require('../../../assets/images/timer.png')}
              style={{ width: 20, height: 20 }}
              imgStyle={{ width: 20, height: 20 }}
            />
            <CountdownTimer
              key={sectionTimerKey} // Force reset when section changes
              textStyle={{
                ...TypoSkin.title3,
                fontWeight: '700',
                color: ColorThemes.light.neutral_text_title_color,
              }}
              initialMinutes={currentSectionTime}
              onHalfTimeReached={handleHalfTimeReached}
              onTimeUp={() => {
                // Auto submit current section when time is up
                handleSectionSubmit();
              }}
            />
          </View>
        }
        onBack={() => {
          navigateBack();
          stopAudio();
        }}
      />

      {/* Section and Exam Selection */}
      <View
        style={{
          backgroundColor: ColorThemes.light.neutral_main_background_color,
          borderBottomWidth: 1,
          borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
        }}>
        {/* Section Selector */}
        {sections.length > 1 && (
          <ScrollView
            ref={scrollSectionRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{}}
            style={{
              marginBottom: 8,
              borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
              borderBottomWidth: 0.1,
            }}>
            {sections.map((section, index) => {
              const isSubmitted = submittedSections.has(section.Id);
              const isCurrentSection = selectedSection?.Id === section.Id;
              const currentIndex = sections.findIndex(
                s => s.Id === selectedSection?.Id,
              );
              // Only allow access to current section or next section if current is submitted
              const canAccess =
                index === currentIndex ||
                (index === currentIndex + 1 &&
                  submittedSections.has(sections[currentIndex]?.Id));

              return (
                <TouchableOpacity
                  key={section.Id}
                  onPress={() =>
                    canAccess ? handleSectionChange(section) : null
                  }
                  disabled={!canAccess}
                  style={{
                    paddingHorizontal: 12,
                    paddingVertical: 4,
                    backgroundColor:
                      ColorThemes.light
                        .Neutral_Background_Color_Absoluteary_Color_Main,
                    borderBottomWidth: 1.5,
                    borderBottomColor: isCurrentSection
                      ? ColorThemes.light.Primary_Color_Main
                      : ColorThemes.light.Neutral_Border_Color_Main,
                    opacity: canAccess ? 1 : 0.5,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 8,
                    }}>
                    <Text
                      style={{
                        ...TypoSkin.body2,
                        fontWeight: isCurrentSection ? '700' : '400',
                        color: isCurrentSection
                          ? ColorThemes.light.Primary_Color_Main
                          : canAccess
                            ? ColorThemes.light.neutral_text_body_color
                            : ColorThemes.light.Neutral_Text_Color_Subtitle,
                      }}>
                      {section.Name} ({section.totalQuestions})
                    </Text>
                    {isSubmitted && (
                      <Winicon
                        src="fill/user interface/check"
                        size={16}
                        color={ColorThemes.light.Success_Color_Main}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        )}

        {/* Exam Info */}
        {selectedSection ? (
          <ScrollView
            nestedScrollEnabled={true}
            style={{ 
              height: 'auto',
              maxHeight: Dimensions.get('window').height / 2.5,
            }} // Thay vì height: '100%'
            contentContainerStyle={{ flexGrow: 1 }}
            >
            {/* Exam Title */}
            <View
              style={{
                alignItems: 'center',
                paddingVertical: 8,
                paddingHorizontal: 16,
              }}>
              {/* check selectedExam?.Title is html */}
              {selectedExam?.Title ? (
                <RenderHTML
                  contentWidth={Dimensions.get('window').width - 32}
                  source={{
                    html: selectedExam?.Title || ' ',
                  }}
                  tagsStyles={{
                    body: {
                      fontSize: 16,
                      lineHeight: 24,
                      fontFamily: 'Noto Sans JP',
                      color: '#18181B',
                    },
                    p: {
                      fontWeight: '600',
                      fontSize: 16,
                      lineHeight: 24,
                      fontFamily: 'Noto Sans JP',
                      color: '#18181B',
                    },
                  }}
                />
              ) : (
                <Text
                  style={{
                    ...TypoSkin.title3,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'center',
                    fontFamily: 'Noto Sans JP',
                  }}>
                  {selectedExam?.Title ||
                    selectedExam?.Name ||
                    `${t('exam.exam')} ${currentExamIndex + 1}`}
                </Text>
              )}
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingHorizontal: 16,
                marginBottom: 8,

              }}>
              <TouchableOpacity
                onPress={() => setShowExamImage(!showExamImage)}>
                <Text
                  style={{
                    ...TypoSkin.body3,
                    color: ColorThemes.light.Primary_Color_Main,
                  }}>
                  {showExamImage ? 'Ẩn ví dụ' : 'Xem ví dụ'}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Audio Player */}
            {selectedExam?.Audio && showExamImage && (
              <View
                style={{
                  paddingHorizontal: 16,
                  paddingVertical: 8,
                  backgroundColor:
                    ColorThemes.light.neutral_main_background_color,
                  borderRadius: 8,
                  marginBottom: 8,
                  gap: 4,
                }}>
                <TouchableOpacity
                  disabled={playedAudios[`exam_${selectedExam.Id}`]}
                  onPress={() => {
                    if (
                      selectedExam?.Audio &&
                      !playedAudios[`exam_${selectedExam.Id}`]
                    ) {
                      const audioUrl = getAudioUrl(selectedExam.Audio);
                      playAudio(audioUrl, `exam_${selectedExam.Id}`);
                    }
                  }}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    opacity: playedAudios[`exam_${selectedExam.Id}`] ? 0.5 : 1,
                  }}>
                  <Winicon
                    src={
                      currentPlayingAudio ===
                        getAudioUrl(selectedExam?.Audio || '')
                        ? 'color/multimedia/button-pause'
                        : 'outline/multimedia/sound'
                    }
                    size={16}
                    color={
                      playedAudios[`exam_${selectedExam.Id}`]
                        ? ColorThemes.light.Neutral_Text_Color_Subtitle
                        : ColorThemes.light.Info_Color_Main
                    }
                  />
                  <Text
                    style={{
                      ...TypoSkin.body3,
                      color: playedAudios[`exam_${selectedExam.Id}`]
                        ? ColorThemes.light.Neutral_Text_Color_Subtitle
                        : ColorThemes.light.Info_Color_Main,
                      marginLeft: 8,
                      fontWeight: '600',
                    }}>
                    {currentPlayingAudio ===
                      getAudioUrl(selectedExam?.Audio || '')
                      ? 'Đang nghe...'
                      : playedAudios[`exam_${selectedExam.Id}`]
                        ? 'Đã nghe'
                        : 'Bấm để nghe'}
                  </Text>
                </TouchableOpacity>
                <Text
                  style={{
                    ...TypoSkin.body3,
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    fontWeight: '600',
                  }}>
                  Bạn chỉ được nghe 1 lần duy nhất
                </Text>
              </View>
            )}

            {/* Collapsible Image */}
            {selectedExam?.Img && showExamImage && (
              <View
                style={{
                  marginHorizontal: 16,
                  marginBottom: 8,
                  borderRadius: 8,
                  overflow: 'hidden',
                  backgroundColor:
                    ColorThemes.light.neutral_main_background_color,
                }}>
                <View style={{ padding: 12 }}>
                  <ClickableImage
                    source={{ uri: ConfigAPI.getValidLink(selectedExam.Img) }}
                    style={{
                      width: '100%',
                      height: 150,
                      borderRadius: 8,
                    }}
                    resizeMode="contain"
                  />
                </View>
              </View>
            )}

            {/* PDF Document */}
            {selectedExam?.Pdf && showExamImage && (
              <PDFViewerV2
                key={`exam-pdf-${selectedExam.Id}-${selectedExam.Pdf}`} // Force re-mount when exam changes
                url={selectedExam.Pdf}
                // height={400}
                showControls={true}
                enablePaging={false}
                fitPolicy={2} // Fit both width and height
                spacing={10}
                password={''}
                scale={1}
                minScale={0.5}
                maxScale={3.0}
                horizontal={false}
                showsPageIndicator={false}
                enableAnnotations={false}
                enableZoom={true}
              />
            )}
          </ScrollView>
        ) : null}
      </View>

      {/* Questions Content */}
      <FlatList
        ref={questionsListRef}
        data={currentQuestions}
        keyExtractor={item => item.Id}
        renderItem={({ item, index }) => (
          <QuestionItem
            question={item}
            questionIndex={getQuestionDisplayIndex(item.Id, index)}
            onAnswerSelect={handleAnswerSelect}
            onPlayAudio={(audioUrl: string, questionId: string) =>
              playAudio(audioUrl, `question_${questionId}`)
            }
            currentPlayingAudio={currentPlayingAudio}
            playedAudios={playedAudios}
            t={t}
          />
        )}
        contentContainerStyle={{
          paddingBottom: 50,
          flexGrow: 1, // Ensure proper layout
        }}
        style={{
          flex: 1,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        removeClippedSubviews={false} // Disable to prevent jumping with dynamic content
        maxToRenderPerBatch={2} // Further reduce for smoother scrolling
        windowSize={3} // Smaller window for better performance
        initialNumToRender={1} // Start with just one item
        updateCellsBatchingPeriod={100} // Batch updates for smoother scrolling
        scrollEventThrottle={16} // Smooth scroll events
        // Remove getItemLayout for dynamic height content
        onScrollToIndexFailed={info => {
          console.log('❌ ScrollToIndex failed:', info);
          // Fallback: scroll to offset with dynamic calculation
          const wait = new Promise(resolve => setTimeout(resolve, 500));
          wait.then(() => {
            // Use scrollToOffset without fixed height assumption
            questionsListRef.current?.scrollToOffset({
              offset: info.index * 400, // Increased estimate for questions with media (image + audio)
              animated: true,
            });
          });
        }}
      />

      {/* Footer with Navigation and Question Counter */}
      <View
        style={{
          backgroundColor:
            ColorThemes.light.Neutral_Background_Color_Absoluteary_Color_Main,
          borderTopWidth: 1,
          borderTopColor: ColorThemes.light.Neutral_Border_Color_Main,
          paddingHorizontal: 16,
          paddingVertical: 12,
        }}>
        {/* Navigation Buttons */}
        {selectedSection &&
          (() => {
            const sectionExams = exams.filter(
              e => e.SectionId === selectedSection.Id,
            );
            const totalExams = sectionExams.length;
            const showNavigation = totalExams > 1;
            const showBackButton = showNavigation && currentExamIndex > 0;
            const showNextButton =
              showNavigation && currentExamIndex < totalExams - 1;

            if (!showNavigation) {
              return null;
            }

            return (
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                {/* Back Button */}
                {totalExams > 1 ? (
                  <AppButton
                    // title={t('exam.previousExam')}
                    backgroundColor={
                      showBackButton
                        ? ColorThemes.light.Primary_Color_Main
                        : ColorThemes.light.Neutral_Background_Color_Disable
                    }
                    textStyle={{
                      ...TypoSkin.buttonText3,
                      color:
                        ColorThemes.light.Neutral_Background_Color_Absolute,
                    }}
                    borderColor={ColorThemes.light.Neutral_Border_Color_Main}
                    prefixIcon={'outline/arrows/left-arrow'}
                    prefixIconSize={16}
                    onPress={!showBackButton ? () => { } : handlePreviousExam}
                    textColor={
                      ColorThemes.light.Neutral_Background_Color_Absolute
                    }
                    containerStyle={{
                      paddingHorizontal: 12,
                      marginRight: 8,
                      borderRadius: 8,
                    }}
                  />
                ) : null}
                {totalExams > 1 ? (
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      justifyContent: 'center',
                    }}>
                    <ScrollView
                      horizontal
                      showsHorizontalScrollIndicator={false}
                      contentContainerStyle={{
                        gap: 4,
                        justifyContent: 'center',
                        flexGrow: 1,
                      }}>
                      {sectionExams.map((exam, index) => (
                        <TouchableOpacity
                          key={exam.Id}
                          onPress={() => handleExamPageClick(index)}
                          style={{
                            width: 32,
                            height: 32,
                            backgroundColor:
                              currentExamIndex === index
                                ? ColorThemes.light.Primary_Color_Main
                                : ColorThemes.light.Neutral_Border_Color_Main,
                            borderRadius: 100,
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}>
                          <Text
                            style={{
                              ...TypoSkin.body2,
                              color:
                                currentExamIndex === index
                                  ? ColorThemes.light
                                    .Neutral_Background_Color_Absolute
                                  : ColorThemes.light.Neutral_Text_Color_Body,
                              ...(currentExamIndex === index && {
                                fontWeight: '700',
                              }),
                            }}>
                            {index + 1}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </ScrollView>
                  </View>
                ) : null}
                {/* Next Button */}
                {totalExams > 1 ? (
                  <AppButton
                    // title={t('exam.nextExam')}
                    backgroundColor={
                      showNextButton
                        ? ColorThemes.light.Primary_Color_Main
                        : ColorThemes.light.Neutral_Background_Color_Disable
                    }
                    textStyle={{
                      ...TypoSkin.buttonText3,
                      color:
                        ColorThemes.light.Neutral_Background_Color_Absolute,
                    }}
                    borderColor="transparent"
                    suffixIcon={'outline/arrows/right-arrow'}
                    suffixIconSize={16}
                    onPress={!showNextButton ? () => { } : handleNextExam}
                    textColor={
                      ColorThemes.light.Neutral_Background_Color_Absolute
                    }
                    containerStyle={{
                      paddingHorizontal: 12,
                      marginLeft: 8,
                      borderRadius: 8,
                    }}
                  />
                ) : null}
              </View>
            );
          })()}
      </View>
    </SafeAreaView>
  );
}
