import {ScrollView, Text, View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import ScreenHeader from '../../../Screen/Layout/header';
import {navigate, navigateBack, RootScreen} from '../../../router/router';
import {AppButton, FLoading, HashTag, ListTile, Winicon} from 'wini-mobile-components';
import WScreenFooter from '../../../Screen/Layout/footer';
import {TypoSkin} from '../../../assets/skin/typography';
import {useNavigation, useRoute} from '@react-navigation/native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ExamType, StatusExam} from '../../../Config/Contanst';
import {useTranslation} from 'react-i18next';
import {useEffect, useState} from 'react';
import {Ultis} from '../../../utils/Utils';
import {examDA} from '../da';
import {DataController} from '../../../base/baseController';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';

const primaryColor = '#E86D64';

export default function OverviewTestNew() {
  const {t} = useTranslation();
  const route = useRoute<any>();
  const {
    id,
    type,
    Step,    
    timeLimit,
    Score,
    name,
    section,
    item,
  } = route.params;
  const navigation = useNavigation<any>();
  const [loading, setLoading] = useState(true);
  const [examInfor, setExamInfor] = useState<any>(item);
  const customer = useSelectorCustomerState().data;
  const [totalQuestions, settotalQuestions] = useState(0);
  function CheckResult({item}: any) {
    const isPass = item?.Status == StatusExam.passed;
    return (
      <Text
        style={{
          ...TypoSkin.title3,
          color: isPass ? ColorThemes.light.Success_Color_Main : primaryColor,
        }}>
        {isPass ? t('exam.pass') : t('exam.fail')}
      </Text>
    );
  }

  const examController = new DataController('Exam');
  const testController = new DataController('Test_Result');

  useEffect(() => {
    // get questions list by examId
    const getAllData = async () => {
      setLoading(true);
      const res = await examController.getPatternList({
        query: `@TestId:{${id}}`,
        pattern: {
          TestId: ['Id', 'Name', 'Time', 'Level', 'SectionId', 'Score'],
          SectionId: ['Id', 'Name', 'Time', 'Score'],
          QuestionId: ['Id', 'Name', 'Title', 'Score', 'Code'],
        },
      });
      var count = 0;
      var tests = [];
      const testResult = await testController.getListSimple({
        query: `@CustomerId: {${customer?.Id}} @TestId: {${id}}`,
      });
      if (testResult.code === 200) {
        count = testResult?.data?.length ?? 0;
        tests = testResult?.data ?? [];
      }
      if (res.code === 200) {
        // total Score in Question add map to item item
        const totalScore = res.Question.reduce(
          (sum: number, question: any) => sum + (question.Score || 1),
          0,
        );
        // res.data has QuestionId and SectionId, i need map them into section compare Id
        // map res.data to section
        const sectionWithQuestions = res.Section.map((sectionItem: any) => {
          // Find all exams that belong to this section

          let sectionExams = res.data.filter(
            (exam: any) => exam.SectionId === sectionItem.Id,
          );

          sectionExams = [...new Set(sectionExams)];
          // Get all question IDs from these exams
          const allQuestionIds = sectionExams
            .map((exam: any) => exam.QuestionId?.split(','))
            .flat()
            .filter(Boolean);

          // Remove duplicates
          const uniqueQuestionIds = [...new Set(allQuestionIds)];

          // Get the actual questions from res.Question and get objects in res.Question
          const questions = uniqueQuestionIds.map((questionId: any) => {
            const question = res.Question.find(
              (question: any) => question.Id === questionId,
            );
            if (!question) return null;
            return {
              ...question,
              Score: question.Score || 1,
            };
          });

          return {
            ...sectionItem,
            questions: questions.filter(Boolean),
            exams: sectionExams, // Also include the exams for this section
            totalScore: questions.reduce(
              (sum: number, question: any) => sum + (question.Score || 1),
              0,
            ),
          };
        });
        setLoading(false);
        setExamInfor({
          ...item,
          sections: sectionWithQuestions,
          totalScore: totalScore,
          CountTest: count,
          TestResults: tests,
          totalQuestions: res.Question.length ?? 0,
        });
      }
    };
    getAllData();
  }, []);

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <ScreenHeader
        title={`Đề thi`}
        backIcon={<Winicon src="outline/arrows/left-arrow" size={20} />}
        onBack={() => {
          navigateBack();
        }}
      />
      {
        loading ? (
          <FLoading
            visible={loading}
            avt={require('../../../assets/appstore.png')}
          />
        ) : <ScrollView style={{flex: 1}}>
        {/* title */}
        <ListTile
          style={{
            borderRadius: 16,

            margin: 16,
            paddingVertical: 16,
            backgroundColor: primaryColor,
          }}
          listtileStyle={{
            gap: 12,
          }}
          leading={
            <Winicon
              src="outline/user interface/file-article"
              size={32}
              color={ColorThemes.light.Neutral_Background_Color_Absolute}
            />
          }
          title={name}
          titleStyle={{
            ...TypoSkin.title1,
            color: ColorThemes.light.Neutral_Background_Color_Absolute,
          }}
          subtitle={
            <Text
              style={{
                ...TypoSkin.body2,
                color: ColorThemes.light.Neutral_Background_Color_Absolute,
              }}>
              {/* DateStart - DateEnd */}
              {Ultis.datetoString(
                new Date(examInfor?.DateStart ?? 0),
                'dd/MM/yyyy',
              ) ?? ''}{' '}
              -{' '}
              {Ultis.datetoString(
                new Date(examInfor?.DateEnd ?? 0),
                'dd/MM/yyyy',
              ) ?? ''}
            </Text>
          }
        />
        {/* các phần thi */}
        <View style={{gap: 7, marginBottom: 16}}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              gap: 8,
            }}>
            <Winicon
              src="outline/user interface/menu-2"
              size={24}
              color={'#112164'}
            />
            <Text
              style={{
                ...TypoSkin.title3,
                color: '#112164',
              }}>
              {t('exam.sections')}
            </Text>
          </View>
          {examInfor?.sections?.map((itemSection: any, index: number) => {
            return (
              <ListTile
                key={index}
                style={{
                  borderRadius: 16,
                  marginHorizontal: 16,
                  backgroundColor: ColorThemes.light.Warning_Color_Background,
                }}
                listtileStyle={{
                  gap: 12,
                }}
                leading={
                  // stt with borderradius 100
                  <View
                    style={{
                      width: 32,
                      height: 32,
                      borderRadius: 100,
                      backgroundColor: primaryColor,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        ...TypoSkin.title3,
                        color:
                          ColorThemes.light.Neutral_Background_Color_Absolute,
                      }}>
                      {index + 1}
                    </Text>
                  </View>
                }
                title={itemSection?.Name ?? ''}
                titleStyle={{
                  ...TypoSkin.title2,
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                }}
                subtitle={
                  <Text
                    style={{
                      ...TypoSkin.body3,
                      color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    }}>
                    Điểm tối thiểu{' '}
                    <Text
                      style={{
                        ...TypoSkin.body3,
                        fontWeight: '700',
                        color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                      }}>
                      {itemSection.Score ?? 0}/{itemSection.totalScore ?? 0}
                    </Text>
                  </Text>
                }
                trailing={
                  // show text with miniues
                  <Text
                    style={{
                      ...TypoSkin.title2,
                      color: primaryColor,
                    }}>
                    {itemSection.Time ?? 0} phút
                  </Text>
                }
              />
            );
          })}
        </View>
        {/* Yeu cau */}
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 16,
            gap: 8,
            marginBottom: 16,
          }}>
          <Winicon
            src="outline/user interface/c-warning"
            size={24}
            color={'#112164'}
          />
          <Text
            style={{
              ...TypoSkin.title3,
              color: '#112164',
            }}>
            {t('exam.requirements')}
          </Text>
        </View>
        <View
          style={{
            flexDirection: 'row',
            width: '100%',
            paddingHorizontal: 16,
            gap: 12,
            marginBottom: 16,
          }}>
          {/* số câu hỏi */}
          <View
            style={{
              flex: 1,
              backgroundColor: ColorThemes.light.Warning_Color_Background,
              padding: 8,
              alignContent: 'center',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 4,
              borderRadius: 16,
            }}>
            <Text
              style={{
                ...TypoSkin.body1,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              }}>
              Số câu hỏi
            </Text>
            <Text
              style={{
                ...TypoSkin.title6,
              }}>
              {examInfor?.totalQuestions ?? 0}
            </Text>
            <Text
              style={{
                ...TypoSkin.body1,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              }}>
              Câu
            </Text>
          </View>

          {/* điểm đạt */}
          <View
            style={{
              flex: 1,
              backgroundColor: ColorThemes.light.Warning_Color_Background,
              borderRadius: 16,
              padding: 8,
              alignContent: 'center',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 4,
            }}>
            <Text
              style={{
                ...TypoSkin.body1,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              }}>
              Điểm đạt
            </Text>
            <Text
              style={{
                ...TypoSkin.title6,
              }}>
              {examInfor?.sections?.reduce((sum: number, item: any) => sum + item.Score, 0) ?? 0}
            </Text>
            <Text
              style={{
                ...TypoSkin.body1,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              }}>
              Điểm
            </Text>
          </View>
          {/* điểm tối đa */}
          <View
            style={{
              flex: 1,
              backgroundColor: ColorThemes.light.Warning_Color_Background,
              borderRadius: 16,
              padding: 8,
              alignContent: 'center',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 4,
            }}>
            <Text
              style={{
                ...TypoSkin.body1,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              }}>
              Điểm tối đa
            </Text>
            <Text
              style={{
                ...TypoSkin.title6,
              }}>
              {examInfor?.totalScore ?? 0}
            </Text>
            <Text
              style={{
                ...TypoSkin.body1,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              }}>
              Điểm
            </Text>
          </View>
        </View>
        <ListTile
          style={{paddingHorizontal: 16, padding: 0}}
          leading={
            <Winicon
              src="outline/files/file-history"
              size={24}
              color="#112164"
            />
          }
          title={
            !examInfor?.Count
              ? `${t('exam.testHistory')}`
              : `${examInfor?.CountTest ?? 0}/${examInfor?.Count ?? 0}`
          }
          titleStyle={{
            ...TypoSkin.title3,
            color: '#112164',
          }}
          bottom={
            !examInfor?.TestResults ? (
              <View
                style={{
                  flex: 1,
                  width: '100%',
                  paddingTop: 16,
                  paddingBottom: 100,
                }}>
                <Text style={{...TypoSkin.body3}}>...</Text>
              </View>
            ) : (
              <View
                style={{
                  flex: 1,
                  width: '100%',
                  paddingTop: 16,
                  paddingBottom: 100,
                }}>
                <View
                  style={{
                    width: '100%',
                    flex: 1,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    borderBottomColor:
                      ColorThemes.light.Neutral_Border_Color_Main,
                    borderBottomWidth: 1,
                    paddingBottom: 8,
                  }}>
                  {/* Time */}
                  <View
                    style={{
                      flex: 1,
                      maxWidth: '30%',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        ...TypoSkin.title3,
                        color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                      }}>
                      Thời gian
                    </Text>
                  </View>
                  {/* result */}
                  <View
                    style={{flex: 1, maxWidth: '30%', alignItems: 'center'}}>
                    <Text
                      style={{
                        ...TypoSkin.title3,
                        color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                      }}>
                      Kết quả
                    </Text>
                  </View>
                  {/* Status */}
                  <View
                    style={{
                      flex: 1,
                      maxWidth: '30%',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        ...TypoSkin.title3,
                        color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                      }}>
                      Trạng thái
                    </Text>
                  </View>
                </View>
                {examInfor?.TestResults?.length ? (
                  examInfor?.TestResults?.map((item: any, index: number) => {
                    return (
                      <View
                        key={index}
                        style={{
                          width: '100%',
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          paddingVertical: 12,
                          borderBottomColor:
                            ColorThemes.light.Neutral_Border_Color_Main,
                          borderBottomWidth:
                            index === examInfor?.TestResults?.length - 1
                              ? 0
                              : 0.5,
                        }}>
                        {/* Time */}
                        <View
                          style={{
                            flex: 1,
                            maxWidth: '30%',
                            alignItems: 'center',
                          }}>
                          <Text
                            style={{
                              ...TypoSkin.body3,
                              color:
                                ColorThemes.light.Neutral_Text_Color_Subtitle,
                              textAlign: 'center',
                              marginTop: 2,
                            }}
                            numberOfLines={2}>
                            {`${Ultis.datetoString(
                              new Date(item.DateCreated ?? 0),
                              'dd/MM/yyyy hh:mm',
                            )}`}
                          </Text>
                        </View>

                        {/* Result */}
                        <View
                          style={{
                            flex: 1,
                            maxWidth: '30%',
                            alignItems: 'center',
                          }}>
                          <Text
                            style={{
                              ...TypoSkin.body3,
                              color: ColorThemes.light.Neutral_Text_Color_Title,
                              fontWeight: '600',
                              textAlign: 'center',
                            }}>
                            {`${item.Score ?? 0}/${examInfor?.totalScore ?? 0}`}
                          </Text>
                        </View>

                        {/* Status */}
                        <View
                          style={{
                            flex: 1,
                            maxWidth: '30%',
                            alignItems: 'center',
                          }}>
                          <CheckResult item={item} />
                        </View>
                      </View>
                    );
                  })
                ) : (
                  <View style={{paddingVertical: 20, alignItems: 'center'}}>
                    <Text
                      style={{
                        ...TypoSkin.body3,
                        color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                      }}>
                      {t('exam.noResults')}
                    </Text>
                  </View>
                )}
              </View>
            )
          }
        />
      </ScrollView>
      }
      {/* content */}
      
      {/*  */}
      <WScreenFooter
        style={{
          flexDirection: 'row',
          gap: 8,
          paddingHorizontal: 16,
          paddingBottom: 32,
        }}>
        <AppButton
          title={t('exam.startExam')}
          backgroundColor={primaryColor}
          borderColor="transparent"
          // disabled={(examInfor?.CountTest ?? 0) >= (examInfor?.Count ?? 0)}
          containerStyle={{
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            navigation.push(RootScreen.DoingTestNewRedesigned, {
              id: id,
              type: type,
              Step: Step,
              testId: id,
            });
          }}
          textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
}
