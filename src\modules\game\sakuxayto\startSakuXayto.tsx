import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Animated,
  Easing,
  ImageBackground,
  ActivityIndicator,
  Alert,
  BackHandler,
} from 'react-native';

import CountBadge from '../components/CountQuestions';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';

import {SafeAreaView} from 'react-native-safe-area-context';

import {BottomGame} from '../components/BottomGame';
import ModelConfirm from '../components/ModelConfirm';
import HintModel from '../components/HintModel';
import GameOverModal from '../components/GameOverModel';

// <PERSON>hông cần GestureHandler cho tap-to-select
import {
  PositionIndicator,
  TappableWordInSentence,
  TappableWordInBank,
} from './components/TapSelectComponents';
import {useFocusEffect, useNavigation, useRoute} from '@react-navigation/native';
import ConfigAPI from '../../../Config/ConfigAPI';

import {useSakuXTHook} from '../../../redux/hook/game/sakuXTHook';
import WinnerModal from './components/WinnerModal';
import Sound from 'react-native-sound';
import {SakuXTWord} from './types/sakuXTTypes';
import {ColorThemes} from '../../../assets/skin/colors';
import {DataController} from '../../../base/baseController';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {randomGID} from '../../../utils/Utils';
import {GameDA} from '../gameDA';
import {set} from 'date-fns';
import GamePauseModal from '../components/ModelPauseGame';
import { useGameAudio } from '../ailatrieuphu/hooks/useGameAudio';

const {width: SCREEN_WIDTH} = Dimensions.get('window');

// Constants
const DEFAULT_SENTENCE = ['Tôi', 'ăn', 'cơm'];
const DEFAULT_DRAGGABLE_WORDS = ['đang', 'sẽ', 'đã', 'rất', 'ngon'];
const DETECTION_RADIUS = 100;
const MAX_SENTENCE_LENGTH = 10;
const SENTENCE_CONTAINER_HEIGHT = {
  NORMAL: 80,
  EXPANDED: 110,
};
const ANIMATION_DURATION = {
  FAST: 250,
  NORMAL: 300,
};

// Không cần interfaces cho drag system nữa

const SakuXayTo = () => {
  const sakuXTHook = useSakuXTHook();
  const gameHook = useGameHook();
  const {isGameOver, messageGameOver, gem} = useSelector(
    (state: RootState) => state.gameStore,
  );
  const {
    availableWords,
    wordsInDropZone,
    wordsOutSideZone,
    questionDone,
    totalQuestion,
    currentQuestion,
    loading,
    error,
    configLoading,
    configError,
    initialized,
    configInitialized,
    // Sử dụng config data từ API
    maxLives,
    currentLives,
    timeLimit,
    isAnswerCorrect,
    timeRemaining,
    feedbackMessage,
    feedbackType,
    gameConfig,
  } = useSelector((state: RootState) => state.SakuXT);
  const navigation = useNavigation<any>();

  const [showModelConfirm, setShowModelConfirm] = useState<boolean>(false);
  const [showHintModel, setShowHintModel] = useState<boolean>(false);
  const [showWinnerModal, setShowWinnerModal] = useState(false);
  const [sentence, setSentence] = useState<string[]>([]);
  const [draggableWords, setDraggableWords] = useState<string[]>([]);

  // Thay thế drag system bằng tap-to-select system
  const [selectedWord, setSelectedWord] = useState<string | null>(null);
  const [selectedWordSource, setSelectedWordSource] = useState<'sentence' | 'bank' | null>(null);
  const [selectedWordIndex, setSelectedWordIndex] = useState<number>(-1);
  const [showPositionIndicators, setShowPositionIndicators] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  // Không cần refs cho drag system nữa

  // Animation refs for sentence expansion
  const sentenceContainerHeight = useRef(
    new Animated.Value(SENTENCE_CONTAINER_HEIGHT.NORMAL),
  ).current;

  //router param
  const route = useRoute<any>();
  const {competenceId, milestoneId} = route.params || {competenceId: '1'};

  const [audioPlayer, setAudioPlayer] = useState<Sound | null>(null);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [isPauseGame, setIsPauseGame] = useState(false);

  // Kiểm tra xem câu hỏi hiện tại có hint không
  const hasHint = currentQuestion?.Suggest && currentQuestion.Suggest.trim() !== '';
  const shouldShowHintButton = Boolean(hasHint);

  // Load game data on component mount
  useEffect(() => {
    initializeGameData();
    return () => {
      sakuXTHook.reset();
      gameHook.resetGame();
    };
  }, [competenceId, milestoneId]);

  const updateSentenceAndDraggableWords = useCallback(() => {
    if (
      initialized &&
      wordsInDropZone.length > 0 &&
      wordsOutSideZone.length > 0
    ) {
      const sentence = wordsInDropZone.map(word => word?.text);
      const draggableW = wordsOutSideZone.map(word => word?.text);
      setSentence([...sentence]);
      setDraggableWords([...draggableW]);
      //init wordsInDropZone
      console.log('init sentence', sentence);
      console.log('init draggableW', draggableW);
    }
  }, [initialized, wordsInDropZone, wordsOutSideZone]);

  useEffect(() => {
    updateSentenceAndDraggableWords();
  }, [updateSentenceAndDraggableWords]);

  useEffect(() => {
    // Sử dụng config từ API nếu có, fallback về default values
    if (gameConfig) {
      gameHook.setData({stateName: 'totalLives', value: gameConfig.maxLives});
      gameHook.setData({stateName: 'currentLives', value: gameConfig.maxLives});
      gameHook.setData({stateName: 'time', value: gameConfig.timeLimit});
      gameHook.setData({stateName: 'isRunTime', value: true});
      gameHook.setData({stateName: 'isGameOver', value: false});
      gameHook.setData({
        stateName: 'gemCost',
        value: gameConfig?.gemHint || 5,
      });
    } else {
      // Fallback to default values
      gameHook.restartGame();
    }
  }, [gameConfig]);

  const initializeGameData = async () => {
    try {
      // Initialize game
      setSentence([]);
      setDraggableWords([]);
      console.log('===== STARTING initializeGameData =====');
      console.log('GameId:gameSKXT', ConfigAPI.gameSKXT);
      console.log('MilestoneId:', milestoneId);
      console.log('CompetenceId:', competenceId);

      // Load game config
      console.log('===== CALLING loadGameConfig =====');
      await sakuXTHook.loadGameConfig(ConfigAPI.gameSKXT);
      console.log('===== loadGameConfig COMPLETED =====');
      // Load questions
      console.log('===== CALLING loadQuestions =====');
      await sakuXTHook.loadQuestions(
        ConfigAPI.gameSKXT,
        milestoneId,
        competenceId,
      );
      console.log('===== loadQuestions COMPLETED =====');

      // Initialize game
      console.log('===== CALLING initializeGame =====');
      sakuXTHook.initializeGame();
      console.log('===== initializeGame COMPLETED =====');
      gameHook.getCurrentScore(ConfigAPI.gameSKXT);
    } catch (err) {
      console.error('===== ERROR in initializeGameData:', err);
    }
  };

  // Không cần update positions cho tap-to-select system

  // Thay thế bằng tap-to-select logic
  const handleWordSelect = useCallback((word: string, source: 'sentence' | 'bank', index: number = -1) => {
    if (isSubmitted) return;

    if (selectedWord === word && selectedWordSource === source && selectedWordIndex === index) {
      // Deselect if clicking the same word
      setSelectedWord(null);
      setSelectedWordSource(null);
      setSelectedWordIndex(-1);
      setShowPositionIndicators(false);
    } else {
      // Select new word
      setSelectedWord(word);
      setSelectedWordSource(source);
      setSelectedWordIndex(index);
      setShowPositionIndicators(true);
    }
  }, [selectedWord, selectedWordSource, selectedWordIndex, isSubmitted]);
const {
      audioState,
      playCorrectAnswer,
      playInCorrectAnswer,
      playWin,
      playGameOver,
      playTimeWarning,
      stopCurrentSound,
      stopTimeWarning,
      clearAllSounds,
      toggleMute,
    } = useGameAudio();
    useFocusEffect(
      useCallback(() => {
        console.log('StartDHBC focused');
  
        return () => {
          console.log('StartDHBC blurred - clearing all sounds');
          clearAllSounds();
        };
      }, [clearAllSounds]),
    );
    // Handle hardware back button
    useEffect(() => {
      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        () => {
          console.log('DHBC: Hardware back button pressed');
          clearAllSounds();
          return false; // Let default behavior handle navigation
        },
      );
      return () => backHandler.remove();
    }, [clearAllSounds]);
  const handlePositionSelect = useCallback((targetIndex: number) => {
    if (!selectedWord || !selectedWordSource) return;

    if (selectedWordSource === 'bank') {
      // Add word from bank to sentence at target position
      if (sentence.length >= MAX_SENTENCE_LENGTH) return;

      setSentence(prevSentence => {
        const newSentence = [...prevSentence];
        newSentence.splice(targetIndex, 0, selectedWord);
        return newSentence;
      });

      setDraggableWords(prev => prev.filter(w => w !== selectedWord));
    } else if (selectedWordSource === 'sentence') {
      // Move word within sentence
      if (targetIndex === selectedWordIndex || targetIndex === selectedWordIndex + 1) {
        // Same position, just deselect
        setSelectedWord(null);
        setSelectedWordSource(null);
        setSelectedWordIndex(-1);
        setShowPositionIndicators(false);
        return;
      }

      setSentence(prevSentence => {
        const newSentence = [...prevSentence];
        const [movedWord] = newSentence.splice(selectedWordIndex, 1);

        let adjustedTargetIndex = targetIndex;
        if (targetIndex > selectedWordIndex) {
          adjustedTargetIndex -= 1;
        }

        newSentence.splice(adjustedTargetIndex, 0, movedWord);
        return newSentence;
      });
    }

    // Clear selection
    setSelectedWord(null);
    setSelectedWordSource(null);
    setSelectedWordIndex(-1);
    setShowPositionIndicators(false);
  }, [selectedWord, selectedWordSource, selectedWordIndex, sentence.length]);

  const handleRemoveFromSentence = useCallback((word: string, index: number) => {
    setSentence(prevSentence => {
      const newSentence = [...prevSentence];
      newSentence.splice(index, 1);
      return newSentence;
    });

    setDraggableWords(prev => [...prev, word]);

    // Clear selection if removing selected word
    if (selectedWord === word && selectedWordSource === 'sentence' && selectedWordIndex === index) {
      setSelectedWord(null);
      setSelectedWordSource(null);
      setSelectedWordIndex(-1);
      setShowPositionIndicators(false);
    }
  }, [selectedWord, selectedWordSource, selectedWordIndex]);

  // Helper function to clear selection
  const clearSelection = useCallback(() => {
    setSelectedWord(null);
    setSelectedWordSource(null);
    setSelectedWordIndex(-1);
    setShowPositionIndicators(false);
  }, []);

  // Debug function để kiểm tra dữ liệu
  const debugGameData = useCallback(() => {
    console.log('=== GAME DEBUG INFO ===');
    console.log('Current question:', currentQuestion);
    console.log('Available words:', availableWords?.map(w => ({
      text: w.text,
      correctPosition: w.correctPosition,
      IsResult: w.IsResult
    })));
    console.log('Words in drop zone:', wordsInDropZone?.map(w => ({
      text: w.text,
      correctPosition: w.correctPosition,
      IsResult: w.IsResult
    })));
    console.log('Words outside zone:', wordsOutSideZone?.map(w => ({
      text: w.text,
      correctPosition: w.correctPosition,
      IsResult: w.IsResult
    })));
    console.log('Current sentence (user order):', sentence);
    console.log('Correct order should be:',
      availableWords
        ?.filter(w => w.IsResult)
        ?.sort((a, b) => a.correctPosition - b.correctPosition)
        ?.map(w => w.text)
    );
    console.log('Initialized:', initialized);
    console.log('========================');
  }, [currentQuestion, availableWords, wordsInDropZone, wordsOutSideZone, sentence, initialized]);

  // Gọi debug khi có thay đổi quan trọng
  useEffect(() => {
    if (initialized && currentQuestion) {
      debugGameData();
    }
  }, [initialized, currentQuestion, debugGameData]);

  const handleSubmit = useCallback(() => {
    console.log('===== CHECK ANSWER CALLED =====');
    console.log('Current sentence:', sentence);
    console.log('Available words:', availableWords);

    // Validation: Kiểm tra điều kiện cần thiết
    if (!availableWords.length || !initialized || !sentence.length) {
      console.warn('Cannot submit: missing required data');
      sakuXTHook.setData({
        stateName: 'isAnswerCorrect',
        value: false,
      });
      sakuXTHook.setData({
        stateName: 'feedbackMessage',
        value: 'Hãy sắp xếp các từ trước khi kiểm tra',
      });
      sakuXTHook.setData({
        stateName: 'feedbackType',
        value: 'error',
      });
      return;
    }

    // Tạo Map để tìm kiếm nhanh hơn
    const wordMap = new Map(
      availableWords.map(word => [
        word.text.normalize().trim().toLowerCase(),
        word
      ])
    );

    // Xây dựng wordsInzone với error handling và thứ tự chính xác
    const wordsInzone: SakuXTWord[] = [];
    for (let i = 0; i < sentence.length; i++) {
      const normalizedText = sentence[i].normalize().trim().toLowerCase();
      const foundWord = wordMap.get(normalizedText);

      if (foundWord) {
        // Tạo copy của word với position chính xác theo thứ tự trong sentence
        const wordWithPosition = {
          ...foundWord,
          currentPosition: i, // Thêm position hiện tại trong sentence
        };
        wordsInzone.push(wordWithPosition);
      } else {
        console.warn(`Word not found in availableWords: ${sentence[i]}`);
        // Không tạo fallback word nữa, báo lỗi luôn
        sakuXTHook.setData({
          stateName: 'isAnswerCorrect',
          value: false,
        });
        sakuXTHook.setData({
          stateName: 'feedbackMessage',
          value: 'Có từ không hợp lệ trong câu trả lời',
        });
        sakuXTHook.setData({
          stateName: 'feedbackType',
          value: 'error',
        });
        return;
      }
    }

    console.log('Words in zone with positions:', wordsInzone);

    sakuXTHook.setData({
      stateName: 'wordsInCheck',
      value: [...wordsInzone],
    });

    // Check answer using Redux action
    sakuXTHook.checkAnswer();
  }, [sentence, availableWords, initialized, sakuXTHook]);

  useEffect(() => {
    setTimeout(() => {
      sakuXTHook.clearFeedback();
      setIsSubmitted(false);
    }, 2000);
  }, [feedbackType]);

  // Auto next question when answer is correct
  useEffect(() => {
    if (isAnswerCorrect === true) {
      playCorrectAnswer();
      if (questionDone + 1 >= totalQuestion && totalQuestion > 0) {
        playWin();
        setTimeout(() => {
          setShowWinnerModal(true);
          gameHook.setData({stateName: 'isRunTime', value: false}); // Dừng timer
        }, 500); // Delay nhỏ để animation hoàn thành
      }

      setTimeout(() => {
        sakuXTHook.nextQuestion();
        sakuXTHook.clearFeedback();
      }, 1000); // Show success message for 1.5 seconds
    }
  }, [isAnswerCorrect]);
  useEffect(() => {
    if (isAnswerCorrect === false) {
      playInCorrectAnswer();
    }
  }, [isAnswerCorrect]);

  // Start timer when game is initialized and config is loaded
  useEffect(() => {
    if (initialized && configInitialized) {
      console.log('===== STARTING TIMER =====');
      sakuXTHook.startTimer();
      gameHook.setData({stateName: 'isRunTime', value: true});
      gameHook.setData({stateName: 'time', value: timeRemaining});
    }
  }, [initialized, configInitialized]);

  // Timer countdown
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (initialized && configInitialized && timeRemaining > 0 && !isPauseGame) {
      interval = setInterval(() => {
        sakuXTHook.updateTimer();
        // Sync with global game state for HeadGame component
        gameHook.setData({stateName: 'time', value: timeRemaining - 1});
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [initialized, configInitialized, timeRemaining, isPauseGame]);

  // Game over when time runs out
  useEffect(() => {
    if (timeRemaining <= 0 && initialized) {
      gameOver('Hết giờ rồi, làm lại nào');
    }
  }, [timeRemaining]);

  const restartGame = () => {
    sakuXTHook.setData({stateName: 'wordsInDropZone', value: []});
    sakuXTHook.setData({stateName: 'wordsOutSideZone', value: []});

    // Reset game state
    setIsSubmitted(false);
    clearSelection();

    // Reset sentence container height
    sentenceContainerHeight.setValue(SENTENCE_CONTAINER_HEIGHT.NORMAL);
    sakuXTHook.clearFeedback();
    sakuXTHook.reset(); // Đã sửa trong reducer để reset currentLives = maxLives
    gameHook.restartGame();
    updateSentenceAndDraggableWords();

    if (configInitialized && timeLimit) {
      console.log(
        `[StartSakuXT] Syncing global timer with SakuXT config: ${timeLimit}s`,
      );
      gameHook.setData({stateName: 'time', value: timeLimit});
      gameHook.setData({stateName: 'isRunTime', value: true});

      // Sync lives với SakuXT config - đảm bảo reset về maxLives
      if (maxLives) {
        console.log(`[StartSakuXT] Resetting lives to maxLives: ${maxLives}`);
        gameHook.setData({stateName: 'totalLives', value: maxLives});
        gameHook.setData({stateName: 'currentLives', value: maxLives});
        // Cũng reset trong SakuXT state để đảm bảo đồng bộ
        sakuXTHook.setData({stateName: 'currentLives', value: maxLives});
      }
    }
  };

  useEffect(() => {
    if (currentLives === 0) {
      gameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);

  // Thua
  const gameOver = (message: string) => {
    playGameOver();
    gameHook.gameOver(message);
    sakuXTHook.initializeGame();
  };

  // Sử dụng gợi ý - Comment: Tạm thời disable vì không có hint từ API
  const useHint = () => {
    if (gem < (gameConfig?.gemHint || 5)) {
      // show model thông báo không đủ gem
      Alert.alert(
        'Thông báo',
        'Bạn không đủ Sakupi để sử dụng gợi ý',
        [
          {
            text: 'OK',
            style: 'cancel',
          },
        ],
        {cancelable: false},
      );
      return;
    }
    gameHook.setData({
      stateName: 'gem',
      value: gem - (gameConfig?.gemHint || 5),
    });
    setShowModelConfirm(false);
    updateScore(gameConfig?.gemHint || 5);
    setShowHintModel(true);
    console.log('Hint feature temporarily disabled - no hint data from API');
  };
  const customerId = useSelectorCustomerState().data.Id;
  const updateScore = async (score: number) => {
    if (score <= 0) return;
    const gamecustomerController = new DataController('GameCustomer');

    const game = {
      Id: randomGID(),
      CustomerId: customerId,
      GameId: ConfigAPI.gameSKXT,
      Stage: milestoneId,
      Competency: competenceId,
      Status: 0,
      DateCreated: new Date().getTime(),
      Score: -(gameConfig?.gemHint || 5),
      HighestScore: 0,
      PlayedAt: new Date().getTime(),
      Name: `Sử dụng gợi ý - Sakuxayto_${milestoneId}`,
    };

    console.log('Tạo bản ghi mới:', game);
    const result = await gamecustomerController.add([game]);
    if (result.code !== 200) {
      return false;
    }
    setShowHintModel(true);
    return true;
  };

  // Setup audio when question changes
  useEffect(() => {
    if (currentQuestion?.audioUrl) {
      setupAudio(currentQuestion.audioUrl);
    }

    // Cleanup previous audio
    return () => {
      if (audioPlayer) {
        audioPlayer.stop();
        audioPlayer.release();
      }
    };
  }, [currentQuestion]);

  // Setup audio player
  const setupAudio = (audioUrl: string) => {
    console.log('===== SETTING UP AUDIO =====');
    console.log('Audio URL:', audioUrl);

    // Release previous audio
    if (audioPlayer) {
      audioPlayer.stop();
      audioPlayer.release();
    }

    // Enable playback in silence mode (iOS)
    Sound.setCategory('Playback');

    // Create new audio instance
    const sound = new Sound(audioUrl, '', audioError => {
      if (audioError) {
        console.error('Failed to load audio:', audioError);
        return;
      }
      console.log('Audio loaded successfully');
      setAudioPlayer(sound);
    });
  };
  // Play audio function
  const playAudio = () => {
    console.log('===== PLAY AUDIO CALLED =====');
    console.log('Audio player:', audioPlayer);
    console.log('Is playing:', isPlayingAudio);

    if (!audioPlayer) {
      console.log('No audio player available');
      return;
    }

    if (isPlayingAudio) {
      // Stop audio if currently playing
      audioPlayer.stop(() => {
        console.log('Audio stopped');
        setIsPlayingAudio(false);
      });
    } else {
      // Play audio
      setIsPlayingAudio(true);
      audioPlayer.play(success => {
        console.log('Audio play finished, success:', success);
        setIsPlayingAudio(false);
      });
    }
  };

  const togglePauseGame = () => {
    console.log(
      `[StartSakuLC] Toggle pause game: ${isPauseGame ? 'Resume' : 'Pause'}`,
    );

    if (!isPauseGame) {
      // Pause game
      gameHook.setData({stateName: 'isRunTime', value: false});
      setIsPauseGame(true);

      // Pause audio nếu đang phát
      if (isPlayingAudio && audioPlayer) {
        audioPlayer.pause();
        setIsPlayingAudio(false);
      }
    } else {
      // Resume game
      gameHook.setData({stateName: 'isRunTime', value: true});
      setIsPauseGame(false);
    }
  };

  return (
    <SafeAreaView style={{flex: 1}}>
    <ImageBackground
      source={require('./assets/backgroundGame.png')}
      style={styles.backgroundImage}
      resizeMode="cover">
      <SafeAreaView edges={['top']} />

      <View style={styles.container}>
        {/* Header */}
        <HeadGame
          gameId={ConfigAPI.gameSKXT}
          isShowSuggest={shouldShowHintButton}
          onUseHint={() => setShowModelConfirm(true)}
          timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
        />
        <View>
          <LineProgressBar progress={(questionDone / totalQuestion) * 100} />
          <View style={styles.progressInfoContainer}>
            <Lives totalLives={maxLives} currentLives={currentLives} />
            <CountBadge current={questionDone} total={totalQuestion} />
          </View>
        </View>
        {/* Loading State */}
        {(loading || configLoading) && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#112164" />
            <Text style={styles.loadingText}>Đang tải câu hỏi...</Text>
          </View>
        )}

        {/* Error State */}
        {(error || configError) && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>
              {error || configError || 'Không có câu hỏi cho game này'}
            </Text>
          </View>
        )}

        {/* Game Content */}
        {initialized &&
        configInitialized &&
        currentQuestion &&
        !error &&
        !configError ? (
          <View style={styles.gameContent}>
            <View style={styles.questionContainer}>
              {currentQuestion.audioUrl ? (
                <TouchableOpacity
                  style={styles.audioButton}
                  onPress={playAudio}>
                  <Text style={styles.audioIcon}>
                    {isPlayingAudio ? '⏸️' : '🔊'}
                  </Text>
                </TouchableOpacity>
              ) : null}
              <Text style={styles.questionText}>
                {currentQuestion.questionText + '('+ currentQuestion.Code+')'}
              </Text>
            </View>
            <View style={styles.container}>
              <View style={styles.mainContent}>
                  <Animated.View style={[styles.plateContainer]}>
                    <ImageBackground
                      source={require('./assets/plate.png')}
                      style={styles.plateBackground}
                      resizeMode="stretch">
                      <View style={styles.row}>
                        {/* Position indicator at start */}
                        <PositionIndicator
                          index={0}
                          isVisible={showPositionIndicators}
                          onPress={handlePositionSelect}
                        />

                        {sentence.map((word, index) => (
                          <View key={`word-${index}`} style={styles.wordSlot}>
                            <TappableWordInSentence
                              word={word}
                              index={index}
                              isSelected={selectedWord === word && selectedWordSource === 'sentence' && selectedWordIndex === index}
                              isSubmitted={isAnswerCorrect ?? false}
                              isError={isAnswerCorrect === false}
                              onPress={(w, i) => handleWordSelect(w, 'sentence', i)}
                              onRemove={handleRemoveFromSentence}
                            />

                            {/* Position indicator after each word */}
                            <PositionIndicator
                              index={index + 1}
                              isVisible={showPositionIndicators}
                              onPress={handlePositionSelect}
                            />
                          </View>
                        ))}
                      </View>
                      {/* Feedback Messages */}
                      {!feedbackMessage ? null : (
                        <View style={styles.feedbackContainer}>
                          <Text
                            style={[
                              styles.feedbackText,
                              feedbackType === 'success'
                                ? styles.successText
                                : styles.errorText,
                            ]}>
                            {feedbackMessage}
                          </Text>
                        </View>
                      )}
                    </ImageBackground>
                  </Animated.View>
                  <View style={styles.buttonContainer}>
                    <TouchableOpacity
                      style={[styles.button, styles.submitButton]}
                      onPress={handleSubmit}
                      disabled={isAnswerCorrect !== null}>
                      <Text style={styles.buttonText}>Kiểm tra đáp án</Text>
                    </TouchableOpacity>
                  </View>
                  <View style={styles.wordBank}>
                    {draggableWords.map((word, index) => (
                      <TappableWordInBank
                        key={`bank-${index}-${word}`}
                        word={word}
                        index={index}
                        isSelected={selectedWord === word && selectedWordSource === 'bank'}
                        onPress={(w, i) => handleWordSelect(w, 'bank', i)}
                      />
                    ))}
                  </View>
                </View>
              </View>
          </View>
        ) : null}
        <BottomGame
          resetGame={() => {
            restartGame();
            gameOver('Thất bại rồi, làm lại nào');
          }}
          backGame={() => {
            navigation.goBack();
          }}
          pauseGame={() => {
            togglePauseGame();
          }}
          volumeGame={() => {}}
        />
        <View style={styles.modalContainer}>
          <ModelConfirm
            isShow={showModelConfirm}
            closeModal={() => setShowModelConfirm(false)}
            onConfirm={useHint}
            message={`Bạn sẽ bị trừ ${gameConfig?.gemHint || 0} Sakupi khi sử dụng trợ giúp này`}
          />
          <HintModel
            isShow={showHintModel}
            closeModal={() => setShowHintModel(false)}
            text={currentQuestion?.Suggest || ''}
          />
          <GameOverModal
            visible={isGameOver}
            restartGame={() => {
              restartGame();
            }}
            onClose={() => {
              // if (currentLives === 0) {
              //   navigation.goBack();
              // }
              restartGame();
            }}
            message={messageGameOver}
            isTimeOut={false}
          />
          <WinnerModal
            visible={showWinnerModal}
            onClose={() => setShowWinnerModal(false)}
            restartGame={restartGame}
            currentLives={currentLives}
            competenceId={competenceId}
            currentMilestoneId={milestoneId}
            gameId={ConfigAPI.gameSKXT}
          />
          <GamePauseModal
            visible={isPauseGame}
            message={'Bạn đang tạm dừng trò chơi'}
            onContinue={() => {
              togglePauseGame();
            }}
          />
        </View>
      </View>
    </ImageBackground></SafeAreaView>
  );
};

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  container: {
    flex: 1,
    marginHorizontal: 16,
    paddingVertical: 16,
  },
  mainContent: {
    flex: 1,
    width: '100%',
  },
  text: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
  },
  questionContainer: {
    width: Dimensions.get('window').width - 32,
    minHeight: 65,
    marginBottom: 20,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    backgroundColor: '#FCF8E8',
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5,
    paddingHorizontal: 8,
  },
  plateContainer: {
    width: Dimensions.get('window').width - 32,
    height: 200,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5,
  },
  plateBackground: {
    width: Dimensions.get('window').width - 32,
    minHeight: 220,
    paddingHorizontal: 20,
    paddingTop: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    width: '100%',
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  slotWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
    flexShrink: 0,
  },
  wordSlot: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2,
  },
  wordBank: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
    padding: 10,
    width: '100%',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
    paddingVertical: 16,
    paddingHorizontal: 16,
    width: '100%',
  },
  button: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    minWidth: 40,
  },
  submitButton: {
    backgroundColor: '#AE2B26',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  gameContent: {
    marginTop: 16,
    flex: 1,
    alignItems: 'center',
  },
  progressInfoContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalContainer: {
    zIndex: 1000,
  },
  errorText: {
    color: '#F44336',
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
  },
  feedbackText: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  successText: {
    color: '#4CAF50',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#112164',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
    paddingHorizontal: 20,
  },
  questionText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
    textAlign: 'center',
  },
  audioButton: {
    marginRight: 10,
    padding: 6,
    borderRadius: 18,
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    flexShrink: 0,
  },
  audioIcon: {
    fontSize: 18,
  },
  feedbackContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 4,
    alignItems: 'center',
  },
});

export default SakuXayTo;
